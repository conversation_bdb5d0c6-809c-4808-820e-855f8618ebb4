[2025-07-10T01:35:24.538Z] ===== NEW ORDER CREATED =====
[2025-07-10T01:35:24.540Z] Total: 0
[2025-07-10T01:35:24.539Z] Order Code: DD29393
[2025-07-10T01:35:24.539Z] Order ID: 13294
[2025-07-10T01:35:24.540Z] Customer: guest
[2025-07-10T01:35:24.540Z] Request URL: /shop-api
[2025-07-10T01:35:24.540Z] Request Method: POST
[2025-07-10T01:35:24.540Z] User Agent: node
[2025-07-10T01:35:24.540Z] Referer: none
[2025-07-10T01:35:24.540Z] IP Address: ************
[2025-07-10T01:35:24.541Z] Order contains NO line items (empty order)
[2025-07-10T01:35:24.542Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:112:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-10T01:35:24.542Z] ===== END ORDER DETAILS =====
[2025-07-10T01:36:12.059Z] ===== NEW ORDER CREATED =====
[2025-07-10T01:36:12.059Z] Order Code: DD29394
[2025-07-10T01:36:12.059Z] Order ID: 13295
[2025-07-10T01:36:12.060Z] Customer: guest
[2025-07-10T01:36:12.060Z] Total: 0
[2025-07-10T01:36:12.060Z] Request URL: /shop-api
[2025-07-10T01:36:12.060Z] Request Method: POST
[2025-07-10T01:36:12.060Z] User Agent: node
[2025-07-10T01:36:12.060Z] Referer: none
[2025-07-10T01:36:12.061Z] IP Address: ************
[2025-07-10T01:36:12.062Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:112:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-10T01:36:12.062Z] ===== END ORDER DETAILS =====
[2025-07-10T01:36:12.061Z] Order contains NO line items (empty order)
[2025-07-10T01:37:01.198Z] ===== NEW ORDER CREATED =====
[2025-07-10T01:37:01.199Z] Order Code: DD29395
[2025-07-10T01:37:01.199Z] Order ID: 13296
[2025-07-10T01:37:01.200Z] Customer: guest
[2025-07-10T01:37:01.201Z] Total: 0
[2025-07-10T01:37:01.201Z] Request URL: /shop-api
[2025-07-10T01:37:01.201Z] Request Method: POST
[2025-07-10T01:37:01.201Z] User Agent: node
[2025-07-10T01:37:01.201Z] Referer: none
[2025-07-10T01:37:01.201Z] IP Address: ************
[2025-07-10T01:37:01.203Z] Order contains NO line items (empty order)
[2025-07-10T01:37:01.204Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:112:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-10T01:37:01.204Z] ===== END ORDER DETAILS =====
[2025-07-10T01:37:39.680Z] Order Code: DD29396
[2025-07-10T01:37:39.680Z] Order ID: 13297
[2025-07-10T01:37:39.679Z] ===== NEW ORDER CREATED =====
[2025-07-10T01:37:39.680Z] Customer: guest
[2025-07-10T01:37:39.680Z] Total: 0
[2025-07-10T01:37:39.680Z] Request URL: /shop-api
[2025-07-10T01:37:39.681Z] Request Method: POST
[2025-07-10T01:37:39.681Z] User Agent: node
[2025-07-10T01:37:39.681Z] Referer: none
[2025-07-10T01:37:39.681Z] IP Address: ************
[2025-07-10T01:37:39.682Z] Order contains NO line items (empty order)
[2025-07-10T01:37:39.682Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:112:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-10T01:37:39.682Z] ===== END ORDER DETAILS =====
[2025-07-10T01:48:26.599Z] ===== NEW ORDER CREATED =====
[2025-07-10T01:48:26.599Z] Order Code: DD29397
[2025-07-10T01:48:26.600Z] Customer: guest
[2025-07-10T01:48:26.600Z] Total: 0
[2025-07-10T01:48:26.601Z] Request URL: /shop-api
[2025-07-10T01:48:26.601Z] Request Method: POST
[2025-07-10T01:48:26.602Z] User Agent: node
[2025-07-10T01:48:26.602Z] Referer: none
[2025-07-10T01:48:26.602Z] IP Address: ************
[2025-07-10T01:48:26.602Z] Order contains NO line items (empty order)
[2025-07-10T01:48:26.603Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:112:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-10T01:48:26.603Z] ===== END ORDER DETAILS =====
[2025-07-10T01:48:26.600Z] Order ID: 13298
[2025-07-10T02:03:42.399Z] ===== NEW ORDER CREATED =====
[2025-07-10T02:03:42.399Z] Order Code: DD29397
[2025-07-10T02:03:42.400Z] Order ID: 13299
[2025-07-10T02:03:42.400Z] Total: 0
[2025-07-10T02:03:42.400Z] Request URL: /shop-api
[2025-07-10T02:03:42.400Z] Request Method: POST
[2025-07-10T02:03:42.400Z] User Agent: node
[2025-07-10T02:03:42.400Z] Referer: none
[2025-07-10T02:03:42.401Z] IP Address: ************
[2025-07-10T02:03:42.402Z] Order contains NO line items (empty order)
[2025-07-10T02:03:42.402Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:112:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-10T02:03:42.402Z] ===== END ORDER DETAILS =====
[2025-07-10T02:03:42.400Z] Customer: guest
[2025-07-10T03:45:36.271Z] ===== NEW ORDER CREATED =====
[2025-07-10T03:45:36.271Z] Order Code: DD29398
[2025-07-10T03:45:36.272Z] Order ID: 13300
[2025-07-10T03:45:36.272Z] Customer: guest
[2025-07-10T03:45:36.272Z] Total: 0
[2025-07-10T03:45:36.272Z] Request URL: /shop-api
[2025-07-10T03:45:36.273Z] Request Method: POST
[2025-07-10T03:45:36.273Z] User Agent: node
[2025-07-10T03:45:36.273Z] Referer: none
[2025-07-10T03:45:36.273Z] IP Address: ************
[2025-07-10T03:45:36.274Z] Order contains NO line items (empty order)
[2025-07-10T03:45:36.274Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:112:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-10T03:45:36.274Z] ===== END ORDER DETAILS =====
[2025-07-10T05:37:46.199Z] Order Code: DD29399
[2025-07-10T05:37:46.199Z] Order ID: 13301
[2025-07-10T05:37:46.198Z] ===== NEW ORDER CREATED =====
[2025-07-10T05:37:46.199Z] Total: 0
[2025-07-10T05:37:46.199Z] Customer: guest
[2025-07-10T05:37:46.200Z] Request URL: /shop-api
[2025-07-10T05:37:46.200Z] Request Method: POST
[2025-07-10T05:37:46.200Z] User Agent: node
[2025-07-10T05:37:46.200Z] Referer: none
[2025-07-10T05:37:46.200Z] IP Address: ************
[2025-07-10T05:37:46.201Z] Order contains NO line items (empty order)
[2025-07-10T05:37:46.201Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:112:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-10T05:37:46.203Z] ===== END ORDER DETAILS =====
[2025-07-10T07:57:33.909Z] ===== NEW ORDER CREATED =====
[2025-07-10T07:57:33.909Z] Order Code: DD29400
[2025-07-10T07:57:33.909Z] Order ID: 13302
[2025-07-10T07:57:33.910Z] Customer: guest
[2025-07-10T07:57:33.910Z] Total: 0
[2025-07-10T07:57:33.910Z] Request URL: /shop-api
[2025-07-10T07:57:33.910Z] Request Method: POST
[2025-07-10T07:57:33.910Z] User Agent: node
[2025-07-10T07:57:33.910Z] Referer: none
[2025-07-10T07:57:33.911Z] IP Address: ************
[2025-07-10T07:57:33.911Z] X-Forwarded-For: ************
[2025-07-10T07:57:33.911Z] Host: damneddesigns.com
[2025-07-10T07:57:33.911Z] Origin: none
[2025-07-10T07:57:33.911Z] Request Body: {"query":"mutation addItemToOrder($productVariantId: ID!, $quantity: Int!) {\n  addItemToOrder(productVariantId: $productVariantId, quantity: $quantity) {\n    ...CustomOrderDetail\n    ... on ErrorResult {\n      errorCode\n      message\n    }\n  }\n}\n\nfragment CustomOrderDetail on Order {\n  __typename\n  id\n  code\n  active\n  createdAt\n  state\n  currencyCode\n  couponCodes\n  discounts {\n    type\n    description\n    amountWithTax\n  }\n  totalQuantity\n  subTotal\n  subTotalWithTax\n  taxSummary {\n    description\n    taxRate\n    taxTotal\n  }\n  shippingWithTax\n  totalWithTax\n  customer {\n    id\n    firstName\n    lastName\n    emailAddress\n  }\n  shippingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  billingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  sh
[2025-07-10T07:57:33.911Z] Connection Remote Address: ::ffff:**********
[2025-07-10T07:57:33.911Z] Connection Remote Port: 53960
[2025-07-10T07:57:33.912Z] Process ID: 134863
[2025-07-10T07:57:33.912Z] Process Title: node /home/<USER>/damneddesigns/backend/dist/index.js
[2025-07-10T07:57:33.912Z] Node Version: v20.19.3
[2025-07-10T07:57:33.912Z] Server Hostname: damned
[2025-07-10T07:57:33.913Z] Stack Trace: Error
    at OrderCreationLoggerPlugin.getCleanStackTrace (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:65:15)
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:139:57)
[2025-07-10T07:57:33.913Z] Order contains NO line items (empty order)
[2025-07-10T07:57:33.913Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:156:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-10T07:57:33.913Z] ===== END ORDER DETAILS =====
[2025-07-10T08:13:49.860Z] ===== NEW ORDER CREATED =====
[2025-07-10T08:13:49.860Z] Order Code: DD29401
[2025-07-10T08:13:49.860Z] Order ID: 13303
[2025-07-10T08:13:49.861Z] Customer: guest
[2025-07-10T08:13:49.862Z] Request URL: /shop-api
[2025-07-10T08:13:49.862Z] Request Method: POST
[2025-07-10T08:13:49.863Z] User Agent: node
[2025-07-10T08:13:49.863Z] Referer: none
[2025-07-10T08:13:49.863Z] IP Address: ************
[2025-07-10T08:13:49.863Z] X-Forwarded-For: ************
[2025-07-10T08:13:49.863Z] Host: damneddesigns.com
[2025-07-10T08:13:49.863Z] Origin: none
[2025-07-10T08:13:49.863Z] Request Body: {"query":"mutation addItemToOrder($productVariantId: ID!, $quantity: Int!) {\n  addItemToOrder(productVariantId: $productVariantId, quantity: $quantity) {\n    ...CustomOrderDetail\n    ... on ErrorResult {\n      errorCode\n      message\n    }\n  }\n}\n\nfragment CustomOrderDetail on Order {\n  __typename\n  id\n  code\n  active\n  createdAt\n  state\n  currencyCode\n  couponCodes\n  discounts {\n    type\n    description\n    amountWithTax\n  }\n  totalQuantity\n  subTotal\n  subTotalWithTax\n  taxSummary {\n    description\n    taxRate\n    taxTotal\n  }\n  shippingWithTax\n  totalWithTax\n  customer {\n    id\n    firstName\n    lastName\n    emailAddress\n  }\n  shippingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  billingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  sh
[2025-07-10T08:13:49.863Z] Connection Remote Address: ::ffff:**********
[2025-07-10T08:13:49.863Z] Connection Remote Port: 55246
[2025-07-10T08:13:49.864Z] Process ID: 134863
[2025-07-10T08:13:49.864Z] Process Title: node /home/<USER>/damneddesigns/backend/dist/index.js
[2025-07-10T08:13:49.864Z] Node Version: v20.19.3
[2025-07-10T08:13:49.865Z] Server Hostname: damned
[2025-07-10T08:13:49.865Z] Stack Trace: Error
    at OrderCreationLoggerPlugin.getCleanStackTrace (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:65:15)
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:139:57)
[2025-07-10T08:13:49.865Z] Order contains NO line items (empty order)
[2025-07-10T08:13:49.865Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:156:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-10T08:13:49.865Z] ===== END ORDER DETAILS =====
[2025-07-10T08:13:49.862Z] Total: 0
[2025-07-10T09:05:01.333Z] ===== NEW ORDER CREATED =====
[2025-07-10T09:05:01.334Z] Order Code: DD29402
[2025-07-10T09:05:01.334Z] Order ID: 13304
[2025-07-10T09:05:01.334Z] Customer: guest
[2025-07-10T09:05:01.335Z] Request URL: /shop-api
[2025-07-10T09:05:01.335Z] Request Method: POST
[2025-07-10T09:05:01.335Z] User Agent: node
[2025-07-10T09:05:01.335Z] Referer: none
[2025-07-10T09:05:01.335Z] IP Address: ************
[2025-07-10T09:05:01.335Z] X-Forwarded-For: ************
[2025-07-10T09:05:01.335Z] Host: damneddesigns.com
[2025-07-10T09:05:01.335Z] Origin: none
[2025-07-10T09:05:01.335Z] Request Body: {"query":"mutation addItemToOrder($productVariantId: ID!, $quantity: Int!) {\n  addItemToOrder(productVariantId: $productVariantId, quantity: $quantity) {\n    ...CustomOrderDetail\n    ... on ErrorResult {\n      errorCode\n      message\n    }\n  }\n}\n\nfragment CustomOrderDetail on Order {\n  __typename\n  id\n  code\n  active\n  createdAt\n  state\n  currencyCode\n  couponCodes\n  discounts {\n    type\n    description\n    amountWithTax\n  }\n  totalQuantity\n  subTotal\n  subTotalWithTax\n  taxSummary {\n    description\n    taxRate\n    taxTotal\n  }\n  shippingWithTax\n  totalWithTax\n  customer {\n    id\n    firstName\n    lastName\n    emailAddress\n  }\n  shippingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  billingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  sh
[2025-07-10T09:05:01.335Z] Connection Remote Address: ::ffff:**********
[2025-07-10T09:05:01.335Z] Connection Remote Port: 57212
[2025-07-10T09:05:01.336Z] Process ID: 134863
[2025-07-10T09:05:01.336Z] Process Title: node /home/<USER>/damneddesigns/backend/dist/index.js
[2025-07-10T09:05:01.336Z] Node Version: v20.19.3
[2025-07-10T09:05:01.336Z] Server Hostname: damned
[2025-07-10T09:05:01.336Z] Stack Trace: Error
    at OrderCreationLoggerPlugin.getCleanStackTrace (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:65:15)
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:139:57)
[2025-07-10T09:05:01.336Z] Order contains NO line items (empty order)
[2025-07-10T09:05:01.336Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:156:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-10T09:05:01.336Z] ===== END ORDER DETAILS =====
[2025-07-10T09:05:01.334Z] Total: 0
[2025-07-10T09:06:34.178Z] ===== NEW ORDER CREATED =====
[2025-07-10T09:06:34.178Z] Order Code: DD29403
[2025-07-10T09:06:34.179Z] Order ID: 13305
[2025-07-10T09:06:34.180Z] Customer: guest
[2025-07-10T09:06:34.180Z] Total: 0
[2025-07-10T09:06:34.180Z] Request URL: /shop-api
[2025-07-10T09:06:34.180Z] Request Method: POST
[2025-07-10T09:06:34.180Z] User Agent: node
[2025-07-10T09:06:34.180Z] Referer: none
[2025-07-10T09:06:34.180Z] IP Address: ************
[2025-07-10T09:06:34.180Z] X-Forwarded-For: ************
[2025-07-10T09:06:34.180Z] Host: damneddesigns.com
[2025-07-10T09:06:34.180Z] Origin: none
[2025-07-10T09:06:34.180Z] Request Body: {"query":"mutation addItemToOrder($productVariantId: ID!, $quantity: Int!) {\n  addItemToOrder(productVariantId: $productVariantId, quantity: $quantity) {\n    ...CustomOrderDetail\n    ... on ErrorResult {\n      errorCode\n      message\n    }\n  }\n}\n\nfragment CustomOrderDetail on Order {\n  __typename\n  id\n  code\n  active\n  createdAt\n  state\n  currencyCode\n  couponCodes\n  discounts {\n    type\n    description\n    amountWithTax\n  }\n  totalQuantity\n  subTotal\n  subTotalWithTax\n  taxSummary {\n    description\n    taxRate\n    taxTotal\n  }\n  shippingWithTax\n  totalWithTax\n  customer {\n    id\n    firstName\n    lastName\n    emailAddress\n  }\n  shippingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  billingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  sh
[2025-07-10T09:06:34.180Z] Connection Remote Address: ::ffff:**********
[2025-07-10T09:06:34.180Z] Connection Remote Port: 35604
[2025-07-10T09:06:34.181Z] Process ID: 134863
[2025-07-10T09:06:34.181Z] Process Title: node /home/<USER>/damneddesigns/backend/dist/index.js
[2025-07-10T09:06:34.181Z] Node Version: v20.19.3
[2025-07-10T09:06:34.181Z] Server Hostname: damned
[2025-07-10T09:06:34.181Z] Stack Trace: Error
    at OrderCreationLoggerPlugin.getCleanStackTrace (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:65:15)
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:139:57)
[2025-07-10T09:06:34.181Z] Order contains NO line items (empty order)
[2025-07-10T09:06:34.182Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:156:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-10T09:06:34.182Z] ===== END ORDER DETAILS =====
[2025-07-10T09:41:30.739Z] ===== NEW ORDER CREATED =====
[2025-07-10T09:41:30.739Z] Order Code: DD29404
[2025-07-10T09:41:30.740Z] Order ID: 13306
[2025-07-10T09:41:30.740Z] Customer: guest
[2025-07-10T09:41:30.740Z] Total: 0
[2025-07-10T09:41:30.741Z] Request URL: /shop-api
[2025-07-10T09:41:30.741Z] Request Method: POST
[2025-07-10T09:41:30.741Z] User Agent: node
[2025-07-10T09:41:30.742Z] Referer: none
[2025-07-10T09:41:30.742Z] IP Address: ************
[2025-07-10T09:41:30.742Z] X-Forwarded-For: ************
[2025-07-10T09:41:30.742Z] Host: damneddesigns.com
[2025-07-10T09:41:30.742Z] Origin: none
[2025-07-10T09:41:30.742Z] Request Body: {"query":"mutation addItemToOrder($productVariantId: ID!, $quantity: Int!) {\n  addItemToOrder(productVariantId: $productVariantId, quantity: $quantity) {\n    ...CustomOrderDetail\n    ... on ErrorResult {\n      errorCode\n      message\n    }\n  }\n}\n\nfragment CustomOrderDetail on Order {\n  __typename\n  id\n  code\n  active\n  createdAt\n  state\n  currencyCode\n  couponCodes\n  discounts {\n    type\n    description\n    amountWithTax\n  }\n  totalQuantity\n  subTotal\n  subTotalWithTax\n  taxSummary {\n    description\n    taxRate\n    taxTotal\n  }\n  shippingWithTax\n  totalWithTax\n  customer {\n    id\n    firstName\n    lastName\n    emailAddress\n  }\n  shippingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  billingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  sh
[2025-07-10T09:41:30.742Z] Connection Remote Address: ::ffff:**********
[2025-07-10T09:41:30.742Z] Connection Remote Port: 49526
[2025-07-10T09:41:30.743Z] Process ID: 134863
[2025-07-10T09:41:30.743Z] Process Title: node /home/<USER>/damneddesigns/backend/dist/index.js
[2025-07-10T09:41:30.743Z] Node Version: v20.19.3
[2025-07-10T09:41:30.743Z] Server Hostname: damned
[2025-07-10T09:41:30.744Z] Stack Trace: Error
    at OrderCreationLoggerPlugin.getCleanStackTrace (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:65:15)
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:139:57)
[2025-07-10T09:41:30.744Z] Order contains NO line items (empty order)
[2025-07-10T09:41:30.744Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:156:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-10T09:41:30.745Z] ===== END ORDER DETAILS =====
[2025-07-10T10:05:43.325Z] ===== NEW ORDER CREATED =====
[2025-07-10T10:05:43.326Z] Order Code: DD29405
[2025-07-10T10:05:43.326Z] Order ID: 13307
[2025-07-10T10:05:43.326Z] Customer: guest
[2025-07-10T10:05:43.326Z] Total: 0
[2025-07-10T10:05:43.326Z] Request URL: /shop-api
[2025-07-10T10:05:43.326Z] Request Method: POST
[2025-07-10T10:05:43.326Z] User Agent: node
[2025-07-10T10:05:43.326Z] Referer: none
[2025-07-10T10:05:43.327Z] IP Address: ************
[2025-07-10T10:05:43.327Z] X-Forwarded-For: ************
[2025-07-10T10:05:43.327Z] Host: damneddesigns.com
[2025-07-10T10:05:43.327Z] Origin: none
[2025-07-10T10:05:43.327Z] Request Body: {"query":"mutation addItemToOrder($productVariantId: ID!, $quantity: Int!) {\n  addItemToOrder(productVariantId: $productVariantId, quantity: $quantity) {\n    ...CustomOrderDetail\n    ... on ErrorResult {\n      errorCode\n      message\n    }\n  }\n}\n\nfragment CustomOrderDetail on Order {\n  __typename\n  id\n  code\n  active\n  createdAt\n  state\n  currencyCode\n  couponCodes\n  discounts {\n    type\n    description\n    amountWithTax\n  }\n  totalQuantity\n  subTotal\n  subTotalWithTax\n  taxSummary {\n    description\n    taxRate\n    taxTotal\n  }\n  shippingWithTax\n  totalWithTax\n  customer {\n    id\n    firstName\n    lastName\n    emailAddress\n  }\n  shippingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  billingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  sh
[2025-07-10T10:05:43.327Z] Connection Remote Address: ::ffff:**********
[2025-07-10T10:05:43.327Z] Connection Remote Port: 57618
[2025-07-10T10:05:43.327Z] Process ID: 134863
[2025-07-10T10:05:43.328Z] Process Title: node /home/<USER>/damneddesigns/backend/dist/index.js
[2025-07-10T10:05:43.328Z] Node Version: v20.19.3
[2025-07-10T10:05:43.328Z] Server Hostname: damned
[2025-07-10T10:05:43.328Z] Stack Trace: Error
    at OrderCreationLoggerPlugin.getCleanStackTrace (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:65:15)
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:139:57)
[2025-07-10T10:05:43.329Z] Order contains NO line items (empty order)
[2025-07-10T10:05:43.330Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:156:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-10T10:05:43.330Z] ===== END ORDER DETAILS =====
[2025-07-10T10:14:55.541Z] ===== NEW ORDER CREATED =====
[2025-07-10T10:14:55.542Z] Order Code: DD29406
[2025-07-10T10:14:55.542Z] Order ID: 13308
[2025-07-10T10:14:55.542Z] Customer: guest
[2025-07-10T10:14:55.542Z] Total: 0
[2025-07-10T10:14:55.542Z] Request URL: /shop-api
[2025-07-10T10:14:55.543Z] Request Method: POST
[2025-07-10T10:14:55.543Z] User Agent: node
[2025-07-10T10:14:55.543Z] IP Address: ************
[2025-07-10T10:14:55.543Z] Referer: none
[2025-07-10T10:14:55.543Z] X-Forwarded-For: ************
[2025-07-10T10:14:55.543Z] Host: damneddesigns.com
[2025-07-10T10:14:55.543Z] Origin: none
[2025-07-10T10:14:55.543Z] Request Body: {"query":"mutation addItemToOrder($productVariantId: ID!, $quantity: Int!) {\n  addItemToOrder(productVariantId: $productVariantId, quantity: $quantity) {\n    ...CustomOrderDetail\n    ... on ErrorResult {\n      errorCode\n      message\n    }\n  }\n}\n\nfragment CustomOrderDetail on Order {\n  __typename\n  id\n  code\n  active\n  createdAt\n  state\n  currencyCode\n  couponCodes\n  discounts {\n    type\n    description\n    amountWithTax\n  }\n  totalQuantity\n  subTotal\n  subTotalWithTax\n  taxSummary {\n    description\n    taxRate\n    taxTotal\n  }\n  shippingWithTax\n  totalWithTax\n  customer {\n    id\n    firstName\n    lastName\n    emailAddress\n  }\n  shippingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  billingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  sh
[2025-07-10T10:14:55.543Z] Connection Remote Address: ::ffff:**********
[2025-07-10T10:14:55.543Z] Connection Remote Port: 34010
[2025-07-10T10:14:55.544Z] Process ID: 134863
[2025-07-10T10:14:55.544Z] Process Title: node /home/<USER>/damneddesigns/backend/dist/index.js
[2025-07-10T10:14:55.544Z] Node Version: v20.19.3
[2025-07-10T10:14:55.544Z] Server Hostname: damned
[2025-07-10T10:14:55.545Z] Stack Trace: Error
    at OrderCreationLoggerPlugin.getCleanStackTrace (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:65:15)
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:139:57)
[2025-07-10T10:14:55.545Z] Order contains NO line items (empty order)
[2025-07-10T10:14:55.545Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:156:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-10T10:14:55.545Z] ===== END ORDER DETAILS =====
[2025-07-10T14:20:55.673Z] ===== NEW ORDER CREATED =====
[2025-07-10T14:20:55.674Z] Order Code: DD29407
[2025-07-10T14:20:55.675Z] Customer: guest
[2025-07-10T14:20:55.675Z] Total: 0
[2025-07-10T14:20:55.675Z] Order ID: 13319
[2025-07-10T14:20:55.676Z] Request URL: /shop-api
[2025-07-10T14:20:55.676Z] Request Method: POST
[2025-07-10T14:20:55.676Z] User Agent: node
[2025-07-10T14:20:55.676Z] Referer: none
[2025-07-10T14:20:55.677Z] X-Forwarded-For: ************
[2025-07-10T14:20:55.677Z] IP Address: ************
[2025-07-10T14:20:55.677Z] Host: damneddesigns.com
[2025-07-10T14:20:55.677Z] Request Body: {"query":"mutation addItemToOrder($productVariantId: ID!, $quantity: Int!) {\n  addItemToOrder(productVariantId: $productVariantId, quantity: $quantity) {\n    ...CustomOrderDetail\n    ... on ErrorResult {\n      errorCode\n      message\n    }\n  }\n}\n\nfragment CustomOrderDetail on Order {\n  __typename\n  id\n  code\n  active\n  createdAt\n  state\n  currencyCode\n  couponCodes\n  discounts {\n    type\n    description\n    amountWithTax\n  }\n  totalQuantity\n  subTotal\n  subTotalWithTax\n  taxSummary {\n    description\n    taxRate\n    taxTotal\n  }\n  shippingWithTax\n  totalWithTax\n  customer {\n    id\n    firstName\n    lastName\n    emailAddress\n  }\n  shippingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  billingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  sh
[2025-07-10T14:20:55.677Z] Origin: none
[2025-07-10T14:20:55.677Z] Connection Remote Address: ::ffff:**********
[2025-07-10T14:20:55.677Z] Connection Remote Port: 34794
[2025-07-10T14:20:55.678Z] Process ID: 134863
[2025-07-10T14:20:55.679Z] Process Title: node /home/<USER>/damneddesigns/backend/dist/index.js
[2025-07-10T14:20:55.679Z] Node Version: v20.19.3
[2025-07-10T14:20:55.679Z] Server Hostname: damned
[2025-07-10T14:20:55.679Z] Order contains NO line items (empty order)
[2025-07-10T14:20:55.679Z] Stack Trace: Error
    at OrderCreationLoggerPlugin.getCleanStackTrace (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:65:15)
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:139:57)
[2025-07-10T14:20:55.680Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:156:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-10T14:20:55.680Z] ===== END ORDER DETAILS =====
[2025-07-10T15:33:43.032Z] ===== NEW ORDER CREATED =====
[2025-07-10T15:33:43.032Z] Order Code: DD29408
[2025-07-10T15:33:43.033Z] Order ID: 13320
[2025-07-10T15:33:43.033Z] Customer: guest
[2025-07-10T15:33:43.034Z] Total: 0
[2025-07-10T15:33:43.034Z] Request URL: /shop-api
[2025-07-10T15:33:43.035Z] User Agent: node
[2025-07-10T15:33:43.035Z] Request Method: POST
[2025-07-10T15:33:43.035Z] Referer: none
[2025-07-10T15:33:43.035Z] IP Address: ************
[2025-07-10T15:33:43.035Z] X-Forwarded-For: ************
[2025-07-10T15:33:43.035Z] Host: damneddesigns.com
[2025-07-10T15:33:43.035Z] Request Body: {"query":"mutation addItemToOrder($productVariantId: ID!, $quantity: Int!) {\n  addItemToOrder(productVariantId: $productVariantId, quantity: $quantity) {\n    ...CustomOrderDetail\n    ... on ErrorResult {\n      errorCode\n      message\n    }\n  }\n}\n\nfragment CustomOrderDetail on Order {\n  __typename\n  id\n  code\n  active\n  createdAt\n  state\n  currencyCode\n  couponCodes\n  discounts {\n    type\n    description\n    amountWithTax\n  }\n  totalQuantity\n  subTotal\n  subTotalWithTax\n  taxSummary {\n    description\n    taxRate\n    taxTotal\n  }\n  shippingWithTax\n  totalWithTax\n  customer {\n    id\n    firstName\n    lastName\n    emailAddress\n  }\n  shippingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  billingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  sh
[2025-07-10T15:33:43.035Z] Connection Remote Address: ::ffff:**********
[2025-07-10T15:33:43.035Z] Connection Remote Port: 43968
[2025-07-10T15:33:43.036Z] Process ID: 134863
[2025-07-10T15:33:43.036Z] Process Title: node /home/<USER>/damneddesigns/backend/dist/index.js
[2025-07-10T15:33:43.036Z] Node Version: v20.19.3
[2025-07-10T15:33:43.036Z] Server Hostname: damned
[2025-07-10T15:33:43.036Z] Stack Trace: Error
    at OrderCreationLoggerPlugin.getCleanStackTrace (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:65:15)
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:139:57)
[2025-07-10T15:33:43.036Z] Order contains NO line items (empty order)
[2025-07-10T15:33:43.037Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:156:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-10T15:33:43.037Z] ===== END ORDER DETAILS =====
[2025-07-10T15:33:43.035Z] Origin: none
[2025-07-10T17:05:51.544Z] ===== NEW ORDER CREATED =====
[2025-07-10T17:05:51.544Z] Order Code: DD29409
[2025-07-10T17:05:51.545Z] Order ID: 13321
[2025-07-10T17:05:51.545Z] Customer: 9629
[2025-07-10T17:05:51.546Z] Request URL: /shop-api
[2025-07-10T17:05:51.546Z] Total: 0
[2025-07-10T17:05:51.546Z] Request Method: POST
[2025-07-10T17:05:51.546Z] User Agent: node
[2025-07-10T17:05:51.546Z] Referer: none
[2025-07-10T17:05:51.546Z] IP Address: ************
[2025-07-10T17:05:51.546Z] X-Forwarded-For: ************
[2025-07-10T17:05:51.546Z] Host: damneddesigns.com
[2025-07-10T17:05:51.546Z] Origin: none
[2025-07-10T17:05:51.546Z] Request Body: {"query":"mutation addItemToOrder($productVariantId: ID!, $quantity: Int!) {\n  addItemToOrder(productVariantId: $productVariantId, quantity: $quantity) {\n    ...CustomOrderDetail\n    ... on ErrorResult {\n      errorCode\n      message\n    }\n  }\n}\n\nfragment CustomOrderDetail on Order {\n  __typename\n  id\n  code\n  active\n  createdAt\n  state\n  currencyCode\n  couponCodes\n  discounts {\n    type\n    description\n    amountWithTax\n  }\n  totalQuantity\n  subTotal\n  subTotalWithTax\n  taxSummary {\n    description\n    taxRate\n    taxTotal\n  }\n  shippingWithTax\n  totalWithTax\n  customer {\n    id\n    firstName\n    lastName\n    emailAddress\n  }\n  shippingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  billingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  sh
[2025-07-10T17:05:51.546Z] Connection Remote Address: ::ffff:**********
[2025-07-10T17:05:51.546Z] Connection Remote Port: 50330
[2025-07-10T17:05:51.547Z] Process ID: 134863
[2025-07-10T17:05:51.547Z] Node Version: v20.19.3
[2025-07-10T17:05:51.547Z] Process Title: node /home/<USER>/damneddesigns/backend/dist/index.js
[2025-07-10T17:05:51.547Z] Server Hostname: damned
[2025-07-10T17:05:51.548Z] Order contains NO line items (empty order)
[2025-07-10T17:05:51.548Z] Stack Trace: Error
    at OrderCreationLoggerPlugin.getCleanStackTrace (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:65:15)
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:139:57)
[2025-07-10T17:05:51.548Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:156:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-10T17:05:51.548Z] ===== END ORDER DETAILS =====
[2025-07-10T17:57:14.524Z] ===== NEW ORDER CREATED =====
[2025-07-10T17:57:14.524Z] Order Code: DD29410
[2025-07-10T17:57:14.525Z] Order ID: 13322
[2025-07-10T17:57:14.525Z] Customer: guest
[2025-07-10T17:57:14.525Z] Total: 0
[2025-07-10T17:57:14.526Z] Request URL: /shop-api
[2025-07-10T17:57:14.526Z] Request Method: POST
[2025-07-10T17:57:14.526Z] User Agent: node
[2025-07-10T17:57:14.526Z] Referer: none
[2025-07-10T17:57:14.526Z] X-Forwarded-For: ************
[2025-07-10T17:57:14.526Z] IP Address: ************
[2025-07-10T17:57:14.526Z] Host: damneddesigns.com
[2025-07-10T17:57:14.526Z] Origin: none
[2025-07-10T17:57:14.526Z] Request Body: {"query":"mutation addItemToOrder($productVariantId: ID!, $quantity: Int!) {\n  addItemToOrder(productVariantId: $productVariantId, quantity: $quantity) {\n    ...CustomOrderDetail\n    ... on ErrorResult {\n      errorCode\n      message\n    }\n  }\n}\n\nfragment CustomOrderDetail on Order {\n  __typename\n  id\n  code\n  active\n  createdAt\n  state\n  currencyCode\n  couponCodes\n  discounts {\n    type\n    description\n    amountWithTax\n  }\n  totalQuantity\n  subTotal\n  subTotalWithTax\n  taxSummary {\n    description\n    taxRate\n    taxTotal\n  }\n  shippingWithTax\n  totalWithTax\n  customer {\n    id\n    firstName\n    lastName\n    emailAddress\n  }\n  shippingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  billingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  sh
[2025-07-10T17:57:14.526Z] Connection Remote Address: ::ffff:**********
[2025-07-10T17:57:14.526Z] Connection Remote Port: 37444
[2025-07-10T17:57:14.527Z] Process ID: 134863
[2025-07-10T17:57:14.527Z] Process Title: node /home/<USER>/damneddesigns/backend/dist/index.js
[2025-07-10T17:57:14.527Z] Node Version: v20.19.3
[2025-07-10T17:57:14.527Z] Server Hostname: damned
[2025-07-10T17:57:14.527Z] Order contains NO line items (empty order)
[2025-07-10T17:57:14.527Z] Stack Trace: Error
    at OrderCreationLoggerPlugin.getCleanStackTrace (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:65:15)
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:139:57)
[2025-07-10T17:57:14.528Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:156:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-10T17:57:14.528Z] ===== END ORDER DETAILS =====
[2025-07-10T19:00:58.639Z] ===== NEW ORDER CREATED =====
[2025-07-10T19:00:58.640Z] Order Code: DD29411
[2025-07-10T19:00:58.640Z] Order ID: 13335
[2025-07-10T19:00:58.640Z] Customer: guest
[2025-07-10T19:00:58.641Z] Total: 0
[2025-07-10T19:00:58.641Z] Request URL: /shop-api
[2025-07-10T19:00:58.641Z] Request Method: POST
[2025-07-10T19:00:58.641Z] User Agent: node
[2025-07-10T19:00:58.641Z] Referer: none
[2025-07-10T19:00:58.641Z] IP Address: ************
[2025-07-10T19:00:58.641Z] X-Forwarded-For: ************
[2025-07-10T19:00:58.641Z] Host: damneddesigns.com
[2025-07-10T19:00:58.641Z] Origin: none
[2025-07-10T19:00:58.641Z] Request Body: {"query":"mutation addItemToOrder($productVariantId: ID!, $quantity: Int!) {\n  addItemToOrder(productVariantId: $productVariantId, quantity: $quantity) {\n    ...CustomOrderDetail\n    ... on ErrorResult {\n      errorCode\n      message\n    }\n  }\n}\n\nfragment CustomOrderDetail on Order {\n  __typename\n  id\n  code\n  active\n  createdAt\n  state\n  currencyCode\n  couponCodes\n  discounts {\n    type\n    description\n    amountWithTax\n  }\n  totalQuantity\n  subTotal\n  subTotalWithTax\n  taxSummary {\n    description\n    taxRate\n    taxTotal\n  }\n  shippingWithTax\n  totalWithTax\n  customer {\n    id\n    firstName\n    lastName\n    emailAddress\n  }\n  shippingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  billingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  sh
[2025-07-10T19:00:58.641Z] Connection Remote Address: ::ffff:**********
[2025-07-10T19:00:58.641Z] Connection Remote Port: 44378
[2025-07-10T19:00:58.644Z] Process ID: 134863
[2025-07-10T19:00:58.644Z] Process Title: node /home/<USER>/damneddesigns/backend/dist/index.js
[2025-07-10T19:00:58.644Z] Node Version: v20.19.3
[2025-07-10T19:00:58.644Z] Server Hostname: damned
[2025-07-10T19:00:58.645Z] Stack Trace: Error
    at OrderCreationLoggerPlugin.getCleanStackTrace (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:65:15)
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:139:57)
[2025-07-10T19:00:58.645Z] Order contains NO line items (empty order)
[2025-07-10T19:00:58.645Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:156:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-10T19:00:58.645Z] ===== END ORDER DETAILS =====
[2025-07-10T20:28:42.500Z] ===== NEW ORDER CREATED =====
[2025-07-10T20:28:42.501Z] Order Code: DD29412
[2025-07-10T20:28:42.501Z] Order ID: 13340
[2025-07-10T20:28:42.501Z] Customer: guest
[2025-07-10T20:28:42.502Z] Total: 0
[2025-07-10T20:28:42.502Z] Request URL: /shop-api
[2025-07-10T20:28:42.502Z] Request Method: POST
[2025-07-10T20:28:42.502Z] User Agent: node
[2025-07-10T20:28:42.502Z] Referer: none
[2025-07-10T20:28:42.502Z] IP Address: ************
[2025-07-10T20:28:42.502Z] X-Forwarded-For: ************
[2025-07-10T20:28:42.502Z] Host: damneddesigns.com
[2025-07-10T20:28:42.502Z] Origin: none
[2025-07-10T20:28:42.502Z] Request Body: {"query":"mutation addItemToOrder($productVariantId: ID!, $quantity: Int!) {\n  addItemToOrder(productVariantId: $productVariantId, quantity: $quantity) {\n    ...CustomOrderDetail\n    ... on ErrorResult {\n      errorCode\n      message\n    }\n  }\n}\n\nfragment CustomOrderDetail on Order {\n  __typename\n  id\n  code\n  active\n  createdAt\n  state\n  currencyCode\n  couponCodes\n  discounts {\n    type\n    description\n    amountWithTax\n  }\n  totalQuantity\n  subTotal\n  subTotalWithTax\n  taxSummary {\n    description\n    taxRate\n    taxTotal\n  }\n  shippingWithTax\n  totalWithTax\n  customer {\n    id\n    firstName\n    lastName\n    emailAddress\n  }\n  shippingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  billingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  sh
[2025-07-10T20:28:42.502Z] Connection Remote Address: ::ffff:**********
[2025-07-10T20:28:42.502Z] Connection Remote Port: 59074
[2025-07-10T20:28:42.503Z] Process ID: 134863
[2025-07-10T20:28:42.503Z] Process Title: node /home/<USER>/damneddesigns/backend/dist/index.js
[2025-07-10T20:28:42.503Z] Node Version: v20.19.3
[2025-07-10T20:28:42.503Z] Server Hostname: damned
[2025-07-10T20:28:42.503Z] Stack Trace: Error
    at OrderCreationLoggerPlugin.getCleanStackTrace (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:65:15)
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:139:57)
[2025-07-10T20:28:42.503Z] Order contains NO line items (empty order)
[2025-07-10T20:28:42.504Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:156:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-10T20:28:42.504Z] ===== END ORDER DETAILS =====
[2025-07-10T21:53:02.754Z] ===== NEW ORDER CREATED =====
[2025-07-10T21:53:02.755Z] Order Code: DD29413
[2025-07-10T21:53:02.755Z] Order ID: 13341
[2025-07-10T21:53:02.755Z] Customer: guest
[2025-07-10T21:53:02.756Z] Total: 0
[2025-07-10T21:53:02.756Z] Request URL: /shop-api
[2025-07-10T21:53:02.756Z] Request Method: POST
[2025-07-10T21:53:02.756Z] User Agent: node
[2025-07-10T21:53:02.756Z] Referer: none
[2025-07-10T21:53:02.756Z] IP Address: ************
[2025-07-10T21:53:02.756Z] X-Forwarded-For: ************
[2025-07-10T21:53:02.756Z] Host: damneddesigns.com
[2025-07-10T21:53:02.756Z] Origin: none
[2025-07-10T21:53:02.756Z] Request Body: {"query":"mutation addItemToOrder($productVariantId: ID!, $quantity: Int!) {\n  addItemToOrder(productVariantId: $productVariantId, quantity: $quantity) {\n    ...CustomOrderDetail\n    ... on ErrorResult {\n      errorCode\n      message\n    }\n  }\n}\n\nfragment CustomOrderDetail on Order {\n  __typename\n  id\n  code\n  active\n  createdAt\n  state\n  currencyCode\n  couponCodes\n  discounts {\n    type\n    description\n    amountWithTax\n  }\n  totalQuantity\n  subTotal\n  subTotalWithTax\n  taxSummary {\n    description\n    taxRate\n    taxTotal\n  }\n  shippingWithTax\n  totalWithTax\n  customer {\n    id\n    firstName\n    lastName\n    emailAddress\n  }\n  shippingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  billingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  sh
[2025-07-10T21:53:02.756Z] Connection Remote Address: ::ffff:**********
[2025-07-10T21:53:02.756Z] Connection Remote Port: 51012
[2025-07-10T21:53:02.757Z] Process ID: 134863
[2025-07-10T21:53:02.757Z] Process Title: node /home/<USER>/damneddesigns/backend/dist/index.js
[2025-07-10T21:53:02.757Z] Node Version: v20.19.3
[2025-07-10T21:53:02.757Z] Server Hostname: damned
[2025-07-10T21:53:02.757Z] Stack Trace: Error
    at OrderCreationLoggerPlugin.getCleanStackTrace (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:65:15)
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:139:57)
[2025-07-10T21:53:02.757Z] Order contains NO line items (empty order)
[2025-07-10T21:53:02.757Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:156:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-10T21:53:02.758Z] ===== END ORDER DETAILS =====
[2025-07-10T22:48:40.682Z] ===== NEW ORDER CREATED =====
[2025-07-10T22:48:40.683Z] Order Code: DD29414
[2025-07-10T22:48:40.683Z] Order ID: 13349
[2025-07-10T22:48:40.683Z] Customer: guest
[2025-07-10T22:48:40.683Z] Total: 0
[2025-07-10T22:48:40.684Z] Request URL: /shop-api
[2025-07-10T22:48:40.684Z] Request Method: POST
[2025-07-10T22:48:40.684Z] User Agent: node
[2025-07-10T22:48:40.684Z] Referer: none
[2025-07-10T22:48:40.684Z] IP Address: ************
[2025-07-10T22:48:40.684Z] X-Forwarded-For: ************
[2025-07-10T22:48:40.684Z] Host: damneddesigns.com
[2025-07-10T22:48:40.684Z] Origin: none
[2025-07-10T22:48:40.684Z] Request Body: {"query":"mutation addItemToOrder($productVariantId: ID!, $quantity: Int!) {\n  addItemToOrder(productVariantId: $productVariantId, quantity: $quantity) {\n    ...CustomOrderDetail\n    ... on ErrorResult {\n      errorCode\n      message\n    }\n  }\n}\n\nfragment CustomOrderDetail on Order {\n  __typename\n  id\n  code\n  active\n  createdAt\n  state\n  currencyCode\n  couponCodes\n  discounts {\n    type\n    description\n    amountWithTax\n  }\n  totalQuantity\n  subTotal\n  subTotalWithTax\n  taxSummary {\n    description\n    taxRate\n    taxTotal\n  }\n  shippingWithTax\n  totalWithTax\n  customer {\n    id\n    firstName\n    lastName\n    emailAddress\n  }\n  shippingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  billingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  sh
[2025-07-10T22:48:40.684Z] Connection Remote Address: ::ffff:**********
[2025-07-10T22:48:40.684Z] Connection Remote Port: 60368
[2025-07-10T22:48:40.685Z] Process ID: 134863
[2025-07-10T22:48:40.685Z] Process Title: node /home/<USER>/damneddesigns/backend/dist/index.js
[2025-07-10T22:48:40.685Z] Node Version: v20.19.3
[2025-07-10T22:48:40.685Z] Server Hostname: damned
[2025-07-10T22:48:40.685Z] Stack Trace: Error
    at OrderCreationLoggerPlugin.getCleanStackTrace (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:65:15)
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:139:57)
[2025-07-10T22:48:40.685Z] Order contains NO line items (empty order)
[2025-07-10T22:48:40.686Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:156:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-10T22:48:40.686Z] ===== END ORDER DETAILS =====
[2025-07-11T11:32:05.699Z] ===== NEW ORDER CREATED =====
[2025-07-11T11:32:05.699Z] Order Code: DD29415
[2025-07-11T11:32:05.700Z] Customer: guest
[2025-07-11T11:32:05.700Z] Total: 0
[2025-07-11T11:32:05.700Z] Order ID: 13352
[2025-07-11T11:32:05.700Z] Request URL: /shop-api
[2025-07-11T11:32:05.700Z] Request Method: POST
[2025-07-11T11:32:05.700Z] User Agent: node
[2025-07-11T11:32:05.700Z] Referer: none
[2025-07-11T11:32:05.701Z] IP Address: ************
[2025-07-11T11:32:05.701Z] X-Forwarded-For: ************
[2025-07-11T11:32:05.701Z] Host: damneddesigns.com
[2025-07-11T11:32:05.701Z] Origin: none
[2025-07-11T11:32:05.701Z] Request Body: {"query":"mutation addItemToOrder($productVariantId: ID!, $quantity: Int!) {\n  addItemToOrder(productVariantId: $productVariantId, quantity: $quantity) {\n    ...CustomOrderDetail\n    ... on ErrorResult {\n      errorCode\n      message\n    }\n  }\n}\n\nfragment CustomOrderDetail on Order {\n  __typename\n  id\n  code\n  active\n  createdAt\n  state\n  currencyCode\n  couponCodes\n  discounts {\n    type\n    description\n    amountWithTax\n  }\n  totalQuantity\n  subTotal\n  subTotalWithTax\n  taxSummary {\n    description\n    taxRate\n    taxTotal\n  }\n  shippingWithTax\n  totalWithTax\n  customer {\n    id\n    firstName\n    lastName\n    emailAddress\n  }\n  shippingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  billingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  sh
[2025-07-11T11:32:05.701Z] Connection Remote Address: ::ffff:**********
[2025-07-11T11:32:05.701Z] Connection Remote Port: 49460
[2025-07-11T11:32:05.702Z] Process ID: 134863
[2025-07-11T11:32:05.702Z] Process Title: node /home/<USER>/damneddesigns/backend/dist/index.js
[2025-07-11T11:32:05.702Z] Node Version: v20.19.3
[2025-07-11T11:32:05.702Z] Server Hostname: damned
[2025-07-11T11:32:05.702Z] Stack Trace: Error
    at OrderCreationLoggerPlugin.getCleanStackTrace (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:65:15)
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:139:57)
[2025-07-11T11:32:05.702Z] Order contains NO line items (empty order)
[2025-07-11T11:32:05.702Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:156:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-11T11:32:05.703Z] ===== END ORDER DETAILS =====
[2025-07-11T13:12:09.454Z] ===== NEW ORDER CREATED =====
[2025-07-11T13:12:09.455Z] Order Code: DD29416
[2025-07-11T13:12:09.455Z] Order ID: 13353
[2025-07-11T13:12:09.456Z] Customer: 15124
[2025-07-11T13:12:09.456Z] Total: 0
[2025-07-11T13:12:09.457Z] Request URL: /shop-api
[2025-07-11T13:12:09.457Z] Request Method: POST
[2025-07-11T13:12:09.457Z] Referer: none
[2025-07-11T13:12:09.457Z] User Agent: node
[2025-07-11T13:12:09.457Z] IP Address: ************
[2025-07-11T13:12:09.457Z] X-Forwarded-For: ************
[2025-07-11T13:12:09.457Z] Host: damneddesigns.com
[2025-07-11T13:12:09.457Z] Origin: none
[2025-07-11T13:12:09.457Z] Request Body: {"query":"mutation addItemToOrder($productVariantId: ID!, $quantity: Int!) {\n  addItemToOrder(productVariantId: $productVariantId, quantity: $quantity) {\n    ...CustomOrderDetail\n    ... on ErrorResult {\n      errorCode\n      message\n    }\n  }\n}\n\nfragment CustomOrderDetail on Order {\n  __typename\n  id\n  code\n  active\n  createdAt\n  state\n  currencyCode\n  couponCodes\n  discounts {\n    type\n    description\n    amountWithTax\n  }\n  totalQuantity\n  subTotal\n  subTotalWithTax\n  taxSummary {\n    description\n    taxRate\n    taxTotal\n  }\n  shippingWithTax\n  totalWithTax\n  customer {\n    id\n    firstName\n    lastName\n    emailAddress\n  }\n  shippingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  billingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  sh
[2025-07-11T13:12:09.457Z] Connection Remote Address: ::ffff:**********
[2025-07-11T13:12:09.457Z] Connection Remote Port: 59474
[2025-07-11T13:12:09.458Z] Process ID: 411295
[2025-07-11T13:12:09.458Z] Process Title: node /home/<USER>/damneddesigns/backend/dist/index.js
[2025-07-11T13:12:09.458Z] Node Version: v20.19.3
[2025-07-11T13:12:09.458Z] Server Hostname: damned
[2025-07-11T13:12:09.459Z] Stack Trace: Error
    at OrderCreationLoggerPlugin.getCleanStackTrace (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:65:15)
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:139:57)
[2025-07-11T13:12:09.459Z] Order contains NO line items (empty order)
[2025-07-11T13:12:09.459Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:156:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-11T13:12:09.459Z] ===== END ORDER DETAILS =====
[2025-07-11T13:43:04.183Z] ===== NEW ORDER CREATED =====
[2025-07-11T13:43:04.183Z] Order Code: DD29417
[2025-07-11T13:43:04.184Z] Order ID: 13354
[2025-07-11T13:43:04.184Z] Customer: guest
[2025-07-11T13:43:04.185Z] Total: 0
[2025-07-11T13:43:04.185Z] Request URL: /shop-api
[2025-07-11T13:43:04.186Z] Request Method: POST
[2025-07-11T13:43:04.186Z] User Agent: node
[2025-07-11T13:43:04.186Z] Referer: none
[2025-07-11T13:43:04.186Z] IP Address: ************
[2025-07-11T13:43:04.186Z] X-Forwarded-For: ************
[2025-07-11T13:43:04.186Z] Host: damneddesigns.com
[2025-07-11T13:43:04.186Z] Origin: none
[2025-07-11T13:43:04.186Z] Connection Remote Address: ::ffff:**********
[2025-07-11T13:43:04.186Z] Connection Remote Port: 58340
[2025-07-11T13:43:04.186Z] Request Body: {"query":"mutation addItemToOrder($productVariantId: ID!, $quantity: Int!) {\n  addItemToOrder(productVariantId: $productVariantId, quantity: $quantity) {\n    ...CustomOrderDetail\n    ... on ErrorResult {\n      errorCode\n      message\n    }\n  }\n}\n\nfragment CustomOrderDetail on Order {\n  __typename\n  id\n  code\n  active\n  createdAt\n  state\n  currencyCode\n  couponCodes\n  discounts {\n    type\n    description\n    amountWithTax\n  }\n  totalQuantity\n  subTotal\n  subTotalWithTax\n  taxSummary {\n    description\n    taxRate\n    taxTotal\n  }\n  shippingWithTax\n  totalWithTax\n  customer {\n    id\n    firstName\n    lastName\n    emailAddress\n  }\n  shippingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  billingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  sh
[2025-07-11T13:43:04.187Z] Process ID: 411295
[2025-07-11T13:43:04.187Z] Process Title: node /home/<USER>/damneddesigns/backend/dist/index.js
[2025-07-11T13:43:04.187Z] Node Version: v20.19.3
[2025-07-11T13:43:04.187Z] Server Hostname: damned
[2025-07-11T13:43:04.188Z] Stack Trace: Error
    at OrderCreationLoggerPlugin.getCleanStackTrace (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:65:15)
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:139:57)
[2025-07-11T13:43:04.188Z] Order contains NO line items (empty order)
[2025-07-11T13:43:04.190Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:156:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-11T13:43:04.191Z] ===== END ORDER DETAILS =====
[2025-07-11T15:44:52.665Z] ===== NEW ORDER CREATED =====
[2025-07-11T15:44:52.665Z] Order Code: DD29418
[2025-07-11T15:44:52.667Z] Total: 0
[2025-07-11T15:44:52.667Z] Request URL: /shop-api
[2025-07-11T15:44:52.667Z] Request Method: POST
[2025-07-11T15:44:52.667Z] User Agent: node
[2025-07-11T15:44:52.665Z] Order ID: 13355
[2025-07-11T15:44:52.667Z] IP Address: ************
[2025-07-11T15:44:52.667Z] Referer: none
[2025-07-11T15:44:52.667Z] X-Forwarded-For: ************
[2025-07-11T15:44:52.667Z] Host: damneddesigns.com
[2025-07-11T15:44:52.667Z] Origin: none
[2025-07-11T15:44:52.667Z] Request Body: {"query":"mutation addItemToOrder($productVariantId: ID!, $quantity: Int!) {\n  addItemToOrder(productVariantId: $productVariantId, quantity: $quantity) {\n    ...CustomOrderDetail\n    ... on ErrorResult {\n      errorCode\n      message\n    }\n  }\n}\n\nfragment CustomOrderDetail on Order {\n  __typename\n  id\n  code\n  active\n  createdAt\n  state\n  currencyCode\n  couponCodes\n  discounts {\n    type\n    description\n    amountWithTax\n  }\n  totalQuantity\n  subTotal\n  subTotalWithTax\n  taxSummary {\n    description\n    taxRate\n    taxTotal\n  }\n  shippingWithTax\n  totalWithTax\n  customer {\n    id\n    firstName\n    lastName\n    emailAddress\n  }\n  shippingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  billingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  sh
[2025-07-11T15:44:52.667Z] Connection Remote Address: ::ffff:**********
[2025-07-11T15:44:52.667Z] Connection Remote Port: 34440
[2025-07-11T15:44:52.668Z] Process ID: 411295
[2025-07-11T15:44:52.668Z] Process Title: node /home/<USER>/damneddesigns/backend/dist/index.js
[2025-07-11T15:44:52.668Z] Node Version: v20.19.3
[2025-07-11T15:44:52.669Z] Server Hostname: damned
[2025-07-11T15:44:52.669Z] Stack Trace: Error
    at OrderCreationLoggerPlugin.getCleanStackTrace (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:65:15)
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:139:57)
[2025-07-11T15:44:52.669Z] Order contains NO line items (empty order)
[2025-07-11T15:44:52.669Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:156:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-11T15:44:52.669Z] ===== END ORDER DETAILS =====
[2025-07-11T15:44:52.666Z] Customer: guest
[2025-07-11T16:22:27.098Z] ===== NEW ORDER CREATED =====
[2025-07-11T16:22:27.099Z] Order Code: DD29419
[2025-07-11T16:22:27.099Z] Order ID: 13356
[2025-07-11T16:22:27.099Z] Customer: guest
[2025-07-11T16:22:27.100Z] Request URL: /shop-api
[2025-07-11T16:22:27.100Z] Request Method: POST
[2025-07-11T16:22:27.100Z] User Agent: node
[2025-07-11T16:22:27.100Z] Referer: none
[2025-07-11T16:22:27.100Z] IP Address: ************
[2025-07-11T16:22:27.100Z] X-Forwarded-For: ************
[2025-07-11T16:22:27.100Z] Host: damneddesigns.com
[2025-07-11T16:22:27.100Z] Origin: none
[2025-07-11T16:22:27.100Z] Request Body: {"query":"mutation addItemToOrder($productVariantId: ID!, $quantity: Int!) {\n  addItemToOrder(productVariantId: $productVariantId, quantity: $quantity) {\n    ...CustomOrderDetail\n    ... on ErrorResult {\n      errorCode\n      message\n    }\n  }\n}\n\nfragment CustomOrderDetail on Order {\n  __typename\n  id\n  code\n  active\n  createdAt\n  state\n  currencyCode\n  couponCodes\n  discounts {\n    type\n    description\n    amountWithTax\n  }\n  totalQuantity\n  subTotal\n  subTotalWithTax\n  taxSummary {\n    description\n    taxRate\n    taxTotal\n  }\n  shippingWithTax\n  totalWithTax\n  customer {\n    id\n    firstName\n    lastName\n    emailAddress\n  }\n  shippingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  billingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  sh
[2025-07-11T16:22:27.100Z] Connection Remote Address: ::ffff:**********
[2025-07-11T16:22:27.100Z] Connection Remote Port: 44442
[2025-07-11T16:22:27.101Z] Process ID: 411295
[2025-07-11T16:22:27.101Z] Process Title: node /home/<USER>/damneddesigns/backend/dist/index.js
[2025-07-11T16:22:27.101Z] Node Version: v20.19.3
[2025-07-11T16:22:27.101Z] Server Hostname: damned
[2025-07-11T16:22:27.101Z] Stack Trace: Error
    at OrderCreationLoggerPlugin.getCleanStackTrace (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:65:15)
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:139:57)
[2025-07-11T16:22:27.101Z] Order contains NO line items (empty order)
[2025-07-11T16:22:27.102Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:156:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-11T16:22:27.102Z] ===== END ORDER DETAILS =====
[2025-07-11T16:22:27.100Z] Total: 0
[2025-07-11T20:53:34.561Z] ===== NEW ORDER CREATED =====
[2025-07-11T20:53:34.562Z] Order ID: 13357
[2025-07-11T20:53:34.562Z] Customer: guest
[2025-07-11T20:53:34.562Z] Total: 0
[2025-07-11T20:53:34.562Z] Request URL: /shop-api
[2025-07-11T20:53:34.562Z] Request Method: POST
[2025-07-11T20:53:34.562Z] User Agent: node
[2025-07-11T20:53:34.562Z] Referer: none
[2025-07-11T20:53:34.563Z] IP Address: ************
[2025-07-11T20:53:34.561Z] Order Code: DD29420
[2025-07-11T20:53:34.563Z] X-Forwarded-For: ************
[2025-07-11T20:53:34.563Z] Host: damneddesigns.com
[2025-07-11T20:53:34.563Z] Origin: none
[2025-07-11T20:53:34.563Z] Request Body: {"query":"mutation addItemToOrder($productVariantId: ID!, $quantity: Int!) {\n  addItemToOrder(productVariantId: $productVariantId, quantity: $quantity) {\n    ...CustomOrderDetail\n    ... on ErrorResult {\n      errorCode\n      message\n    }\n  }\n}\n\nfragment CustomOrderDetail on Order {\n  __typename\n  id\n  code\n  active\n  createdAt\n  state\n  currencyCode\n  couponCodes\n  discounts {\n    type\n    description\n    amountWithTax\n  }\n  totalQuantity\n  subTotal\n  subTotalWithTax\n  taxSummary {\n    description\n    taxRate\n    taxTotal\n  }\n  shippingWithTax\n  totalWithTax\n  customer {\n    id\n    firstName\n    lastName\n    emailAddress\n  }\n  shippingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  billingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  sh
[2025-07-11T20:53:34.563Z] Connection Remote Address: ::ffff:**********
[2025-07-11T20:53:34.563Z] Connection Remote Port: 33856
[2025-07-11T20:53:34.564Z] Process ID: 471661
[2025-07-11T20:53:34.564Z] Process Title: node /home/<USER>/damneddesigns/backend/dist/index.js
[2025-07-11T20:53:34.564Z] Node Version: v20.19.3
[2025-07-11T20:53:34.564Z] Stack Trace: Error
    at OrderCreationLoggerPlugin.getCleanStackTrace (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:65:15)
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:139:57)
[2025-07-11T20:53:34.564Z] Order contains NO line items (empty order)
[2025-07-11T20:53:34.564Z] Server Hostname: damned
[2025-07-11T20:53:34.564Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:156:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-11T20:53:34.565Z] ===== END ORDER DETAILS =====
[2025-07-12T02:46:32.849Z] ===== NEW ORDER CREATED =====
[2025-07-12T02:46:32.849Z] Order Code: DD29421
[2025-07-12T02:46:32.850Z] Order ID: 13358
[2025-07-12T02:46:32.851Z] Customer: guest
[2025-07-12T02:46:32.851Z] Total: 0
[2025-07-12T02:46:32.851Z] Request URL: /shop-api
[2025-07-12T02:46:32.851Z] Request Method: POST
[2025-07-12T02:46:32.851Z] User Agent: node
[2025-07-12T02:46:32.851Z] Referer: none
[2025-07-12T02:46:32.851Z] IP Address: ************
[2025-07-12T02:46:32.851Z] X-Forwarded-For: ************
[2025-07-12T02:46:32.851Z] Host: damneddesigns.com
[2025-07-12T02:46:32.851Z] Origin: none
[2025-07-12T02:46:32.851Z] Request Body: {"query":"mutation addItemToOrder($productVariantId: ID!, $quantity: Int!) {\n  addItemToOrder(productVariantId: $productVariantId, quantity: $quantity) {\n    ...CustomOrderDetail\n    ... on ErrorResult {\n      errorCode\n      message\n    }\n  }\n}\n\nfragment CustomOrderDetail on Order {\n  __typename\n  id\n  code\n  active\n  createdAt\n  state\n  currencyCode\n  couponCodes\n  discounts {\n    type\n    description\n    amountWithTax\n  }\n  totalQuantity\n  subTotal\n  subTotalWithTax\n  taxSummary {\n    description\n    taxRate\n    taxTotal\n  }\n  shippingWithTax\n  totalWithTax\n  customer {\n    id\n    firstName\n    lastName\n    emailAddress\n  }\n  shippingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  billingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  sh
[2025-07-12T02:46:32.851Z] Connection Remote Address: ::ffff:**********
[2025-07-12T02:46:32.851Z] Connection Remote Port: 51698
[2025-07-12T02:46:32.852Z] Process ID: 471661
[2025-07-12T02:46:32.852Z] Process Title: node /home/<USER>/damneddesigns/backend/dist/index.js
[2025-07-12T02:46:32.852Z] Node Version: v20.19.3
[2025-07-12T02:46:32.854Z] Server Hostname: damned
[2025-07-12T02:46:32.855Z] Stack Trace: Error
    at OrderCreationLoggerPlugin.getCleanStackTrace (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:65:15)
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:139:57)
[2025-07-12T02:46:32.855Z] Order contains NO line items (empty order)
[2025-07-12T02:46:32.855Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:156:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-12T02:46:32.855Z] ===== END ORDER DETAILS =====
[2025-07-12T02:48:08.760Z] ===== NEW ORDER CREATED =====
[2025-07-12T02:48:08.760Z] Order Code: DD29422
[2025-07-12T02:48:08.761Z] Order ID: 13359
[2025-07-12T02:48:08.761Z] Customer: guest
[2025-07-12T02:48:08.761Z] Total: 0
[2025-07-12T02:48:08.761Z] Request URL: /shop-api
[2025-07-12T02:48:08.761Z] Request Method: POST
[2025-07-12T02:48:08.761Z] User Agent: node
[2025-07-12T02:48:08.761Z] Referer: none
[2025-07-12T02:48:08.762Z] IP Address: ************
[2025-07-12T02:48:08.762Z] X-Forwarded-For: ************
[2025-07-12T02:48:08.762Z] Host: damneddesigns.com
[2025-07-12T02:48:08.762Z] Origin: none
[2025-07-12T02:48:08.762Z] Request Body: {"query":"mutation addItemToOrder($productVariantId: ID!, $quantity: Int!) {\n  addItemToOrder(productVariantId: $productVariantId, quantity: $quantity) {\n    ...CustomOrderDetail\n    ... on ErrorResult {\n      errorCode\n      message\n    }\n  }\n}\n\nfragment CustomOrderDetail on Order {\n  __typename\n  id\n  code\n  active\n  createdAt\n  state\n  currencyCode\n  couponCodes\n  discounts {\n    type\n    description\n    amountWithTax\n  }\n  totalQuantity\n  subTotal\n  subTotalWithTax\n  taxSummary {\n    description\n    taxRate\n    taxTotal\n  }\n  shippingWithTax\n  totalWithTax\n  customer {\n    id\n    firstName\n    lastName\n    emailAddress\n  }\n  shippingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  billingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  sh
[2025-07-12T02:48:08.762Z] Connection Remote Address: ::ffff:**********
[2025-07-12T02:48:08.762Z] Connection Remote Port: 38126
[2025-07-12T02:48:08.762Z] Process ID: 471661
[2025-07-12T02:48:08.762Z] Process Title: node /home/<USER>/damneddesigns/backend/dist/index.js
[2025-07-12T02:48:08.763Z] Node Version: v20.19.3
[2025-07-12T02:48:08.763Z] Server Hostname: damned
[2025-07-12T02:48:08.763Z] Stack Trace: Error
    at OrderCreationLoggerPlugin.getCleanStackTrace (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:65:15)
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:139:57)
[2025-07-12T02:48:08.763Z] Order contains NO line items (empty order)
[2025-07-12T02:48:08.763Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:156:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-12T02:48:08.763Z] ===== END ORDER DETAILS =====
[2025-07-12T05:56:00.651Z] ===== NEW ORDER CREATED =====
[2025-07-12T05:56:00.652Z] Order Code: DD29423
[2025-07-12T05:56:00.652Z] Order ID: 13360
[2025-07-12T05:56:00.653Z] Customer: guest
[2025-07-12T05:56:00.653Z] Total: 0
[2025-07-12T05:56:00.653Z] Request URL: /shop-api
[2025-07-12T05:56:00.653Z] Request Method: POST
[2025-07-12T05:56:00.653Z] User Agent: node
[2025-07-12T05:56:00.653Z] Referer: none
[2025-07-12T05:56:00.653Z] IP Address: ************
[2025-07-12T05:56:00.653Z] X-Forwarded-For: ************
[2025-07-12T05:56:00.654Z] Host: damneddesigns.com
[2025-07-12T05:56:00.654Z] Origin: none
[2025-07-12T05:56:00.654Z] Request Body: {"query":"mutation addItemToOrder($productVariantId: ID!, $quantity: Int!) {\n  addItemToOrder(productVariantId: $productVariantId, quantity: $quantity) {\n    ...CustomOrderDetail\n    ... on ErrorResult {\n      errorCode\n      message\n    }\n  }\n}\n\nfragment CustomOrderDetail on Order {\n  __typename\n  id\n  code\n  active\n  createdAt\n  state\n  currencyCode\n  couponCodes\n  discounts {\n    type\n    description\n    amountWithTax\n  }\n  totalQuantity\n  subTotal\n  subTotalWithTax\n  taxSummary {\n    description\n    taxRate\n    taxTotal\n  }\n  shippingWithTax\n  totalWithTax\n  customer {\n    id\n    firstName\n    lastName\n    emailAddress\n  }\n  shippingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  billingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  sh
[2025-07-12T05:56:00.654Z] Connection Remote Address: ::ffff:**********
[2025-07-12T05:56:00.654Z] Connection Remote Port: 44304
[2025-07-12T05:56:00.655Z] Process ID: 471661
[2025-07-12T05:56:00.655Z] Process Title: node /home/<USER>/damneddesigns/backend/dist/index.js
[2025-07-12T05:56:00.655Z] Node Version: v20.19.3
[2025-07-12T05:56:00.655Z] Server Hostname: damned
[2025-07-12T05:56:00.655Z] Stack Trace: Error
    at OrderCreationLoggerPlugin.getCleanStackTrace (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:65:15)
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:139:57)
[2025-07-12T05:56:00.655Z] Order contains NO line items (empty order)
[2025-07-12T05:56:00.656Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:156:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-12T05:56:00.656Z] ===== END ORDER DETAILS =====
[2025-07-13T14:06:01.611Z] ===== NEW ORDER CREATED =====
[2025-07-13T14:06:01.613Z] Order ID: 13363
[2025-07-13T14:06:01.614Z] Customer: guest
[2025-07-13T14:06:01.615Z] Total: 0
[2025-07-13T14:06:01.615Z] Request URL: /shop-api
[2025-07-13T14:06:01.615Z] Request Method: POST
[2025-07-13T14:06:01.615Z] User Agent: node
[2025-07-13T14:06:01.615Z] Referer: none
[2025-07-13T14:06:01.615Z] IP Address: ************
[2025-07-13T14:06:01.615Z] X-Forwarded-For: ************
[2025-07-13T14:06:01.616Z] Host: damneddesigns.com
[2025-07-13T14:06:01.616Z] Origin: none
[2025-07-13T14:06:01.612Z] Order Code: DD29426
[2025-07-13T14:06:01.616Z] Request Body: {"query":"mutation addItemToOrder($productVariantId: ID!, $quantity: Int!) {\n  addItemToOrder(productVariantId: $productVariantId, quantity: $quantity) {\n    ...CustomOrderDetail\n    ... on ErrorResult {\n      errorCode\n      message\n    }\n  }\n}\n\nfragment CustomOrderDetail on Order {\n  __typename\n  id\n  code\n  active\n  createdAt\n  state\n  currencyCode\n  couponCodes\n  discounts {\n    type\n    description\n    amountWithTax\n  }\n  totalQuantity\n  subTotal\n  subTotalWithTax\n  taxSummary {\n    description\n    taxRate\n    taxTotal\n  }\n  shippingWithTax\n  totalWithTax\n  customer {\n    id\n    firstName\n    lastName\n    emailAddress\n  }\n  shippingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  billingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  sh
[2025-07-13T14:06:01.616Z] Connection Remote Address: ::ffff:**********
[2025-07-13T14:06:01.616Z] Connection Remote Port: 51798
[2025-07-13T14:06:01.617Z] Process ID: 678758
[2025-07-13T14:06:01.617Z] Process Title: node /home/<USER>/damneddesigns/backend/dist/index.js
[2025-07-13T14:06:01.617Z] Node Version: v20.19.3
[2025-07-13T14:06:01.617Z] Server Hostname: damned
[2025-07-13T14:06:01.617Z] Stack Trace: Error
    at OrderCreationLoggerPlugin.getCleanStackTrace (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:65:15)
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:139:57)
[2025-07-13T14:06:01.617Z] Order contains NO line items (empty order)
[2025-07-13T14:06:01.618Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:156:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-13T14:06:01.618Z] ===== END ORDER DETAILS =====
[2025-07-13T14:16:27.944Z] ===== NEW ORDER CREATED =====
[2025-07-13T14:16:27.946Z] Order Code: DD29427
[2025-07-13T14:16:27.946Z] Order ID: 13364
[2025-07-13T14:16:27.946Z] Customer: guest
[2025-07-13T14:16:27.947Z] Total: 0
[2025-07-13T14:16:27.947Z] Request URL: /shop-api
[2025-07-13T14:16:27.947Z] Request Method: POST
[2025-07-13T14:16:27.947Z] User Agent: node
[2025-07-13T14:16:27.947Z] Referer: none
[2025-07-13T14:16:27.947Z] IP Address: ************
[2025-07-13T14:16:27.947Z] X-Forwarded-For: ************
[2025-07-13T14:16:27.947Z] Origin: none
[2025-07-13T14:16:27.947Z] Request Body: {"query":"mutation addItemToOrder($productVariantId: ID!, $quantity: Int!) {\n  addItemToOrder(productVariantId: $productVariantId, quantity: $quantity) {\n    ...CustomOrderDetail\n    ... on ErrorResult {\n      errorCode\n      message\n    }\n  }\n}\n\nfragment CustomOrderDetail on Order {\n  __typename\n  id\n  code\n  active\n  createdAt\n  state\n  currencyCode\n  couponCodes\n  discounts {\n    type\n    description\n    amountWithTax\n  }\n  totalQuantity\n  subTotal\n  subTotalWithTax\n  taxSummary {\n    description\n    taxRate\n    taxTotal\n  }\n  shippingWithTax\n  totalWithTax\n  customer {\n    id\n    firstName\n    lastName\n    emailAddress\n  }\n  shippingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  billingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  sh
[2025-07-13T14:16:27.947Z] Connection Remote Address: ::ffff:**********
[2025-07-13T14:16:27.947Z] Connection Remote Port: 58064
[2025-07-13T14:16:27.947Z] Host: damneddesigns.com
[2025-07-13T14:16:27.949Z] Process ID: 678758
[2025-07-13T14:16:27.949Z] Process Title: node /home/<USER>/damneddesigns/backend/dist/index.js
[2025-07-13T14:16:27.949Z] Node Version: v20.19.3
[2025-07-13T14:16:27.949Z] Server Hostname: damned
[2025-07-13T14:16:27.949Z] Stack Trace: Error
    at OrderCreationLoggerPlugin.getCleanStackTrace (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:65:15)
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:139:57)
[2025-07-13T14:16:27.949Z] Order contains NO line items (empty order)
[2025-07-13T14:16:27.949Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:156:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-13T14:16:27.949Z] ===== END ORDER DETAILS =====
[2025-07-13T14:16:49.210Z] ===== NEW ORDER CREATED =====
[2025-07-13T14:16:49.211Z] Order Code: DD29428
[2025-07-13T14:16:49.211Z] Order ID: 13365
[2025-07-13T14:16:49.212Z] Customer: guest
[2025-07-13T14:16:49.212Z] Total: 0
[2025-07-13T14:16:49.213Z] Request URL: /shop-api
[2025-07-13T14:16:49.213Z] Request Method: POST
[2025-07-13T14:16:49.213Z] User Agent: node
[2025-07-13T14:16:49.214Z] Referer: none
[2025-07-13T14:16:49.214Z] IP Address: ************
[2025-07-13T14:16:49.214Z] X-Forwarded-For: ************
[2025-07-13T14:16:49.214Z] Host: damneddesigns.com
[2025-07-13T14:16:49.214Z] Origin: none
[2025-07-13T14:16:49.214Z] Request Body: {"query":"mutation transitionOrderToState($state: String!) {\n  transitionOrderToState(state: $state) {\n    ...CustomOrderDetail\n    ... on ErrorResult {\n      errorCode\n      message\n    }\n  }\n}\n\nfragment CustomOrderDetail on Order {\n  __typename\n  id\n  code\n  active\n  createdAt\n  state\n  currencyCode\n  couponCodes\n  discounts {\n    type\n    description\n    amountWithTax\n  }\n  totalQuantity\n  subTotal\n  subTotalWithTax\n  taxSummary {\n    description\n    taxRate\n    taxTotal\n  }\n  shippingWithTax\n  totalWithTax\n  customer {\n    id\n    firstName\n    lastName\n    emailAddress\n  }\n  shippingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  billingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  shippingLines {\n    shippingMethod {\n      id\n    
[2025-07-13T14:16:49.214Z] Connection Remote Address: ::ffff:**********
[2025-07-13T14:16:49.214Z] Connection Remote Port: 49142
[2025-07-13T14:16:49.215Z] Process ID: 678801
[2025-07-13T14:16:49.216Z] Process Title: node /home/<USER>/damneddesigns/backend/dist/index.js
[2025-07-13T14:16:49.216Z] Node Version: v20.19.3
[2025-07-13T14:16:49.216Z] Server Hostname: damned
[2025-07-13T14:16:49.217Z] Stack Trace: Error
    at OrderCreationLoggerPlugin.getCleanStackTrace (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:65:15)
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:139:57)
[2025-07-13T14:16:49.217Z] Order contains NO line items (empty order)
[2025-07-13T14:16:49.217Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:156:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-13T14:16:49.218Z] ===== END ORDER DETAILS =====
[2025-07-13T14:19:37.912Z] ===== NEW ORDER CREATED =====
[2025-07-13T14:19:37.913Z] Order Code: DD29429
[2025-07-13T14:19:37.914Z] Order ID: 13366
[2025-07-13T14:19:37.914Z] Customer: guest
[2025-07-13T14:19:37.915Z] Total: 0
[2025-07-13T14:19:37.915Z] Request URL: /shop-api
[2025-07-13T14:19:37.915Z] Request Method: POST
[2025-07-13T14:19:37.915Z] User Agent: node
[2025-07-13T14:19:37.915Z] Referer: none
[2025-07-13T14:19:37.915Z] IP Address: ************
[2025-07-13T14:19:37.915Z] X-Forwarded-For: ************
[2025-07-13T14:19:37.915Z] Host: damneddesigns.com
[2025-07-13T14:19:37.915Z] Origin: none
[2025-07-13T14:19:37.915Z] Request Body: {"query":"mutation addItemToOrder($productVariantId: ID!, $quantity: Int!) {\n  addItemToOrder(productVariantId: $productVariantId, quantity: $quantity) {\n    ...CustomOrderDetail\n    ... on ErrorResult {\n      errorCode\n      message\n    }\n  }\n}\n\nfragment CustomOrderDetail on Order {\n  __typename\n  id\n  code\n  active\n  createdAt\n  state\n  currencyCode\n  couponCodes\n  discounts {\n    type\n    description\n    amountWithTax\n  }\n  totalQuantity\n  subTotal\n  subTotalWithTax\n  taxSummary {\n    description\n    taxRate\n    taxTotal\n  }\n  shippingWithTax\n  totalWithTax\n  customer {\n    id\n    firstName\n    lastName\n    emailAddress\n  }\n  shippingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  billingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  sh
[2025-07-13T14:19:37.915Z] Connection Remote Address: ::ffff:**********
[2025-07-13T14:19:37.915Z] Connection Remote Port: 43068
[2025-07-13T14:19:37.917Z] Process ID: 678758
[2025-07-13T14:19:37.917Z] Process Title: node /home/<USER>/damneddesigns/backend/dist/index.js
[2025-07-13T14:19:37.917Z] Node Version: v20.19.3
[2025-07-13T14:19:37.917Z] Server Hostname: damned
[2025-07-13T14:19:37.917Z] Stack Trace: Error
    at OrderCreationLoggerPlugin.getCleanStackTrace (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:65:15)
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:139:57)
[2025-07-13T14:19:37.917Z] Order contains NO line items (empty order)
[2025-07-13T14:19:37.918Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:156:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-13T14:19:37.918Z] ===== END ORDER DETAILS =====
[2025-07-13T14:36:49.759Z] ===== NEW ORDER CREATED =====
[2025-07-13T14:36:49.760Z] Order Code: DD29430
[2025-07-13T14:36:49.760Z] Order ID: 13367
[2025-07-13T14:36:49.761Z] Customer: guest
[2025-07-13T14:36:49.761Z] Total: 0
[2025-07-13T14:36:49.761Z] Request URL: /shop-api
[2025-07-13T14:36:49.761Z] Request Method: POST
[2025-07-13T14:36:49.761Z] User Agent: node
[2025-07-13T14:36:49.762Z] Referer: none
[2025-07-13T14:36:49.762Z] IP Address: ************
[2025-07-13T14:36:49.762Z] X-Forwarded-For: ************
[2025-07-13T14:36:49.762Z] Host: damneddesigns.com
[2025-07-13T14:36:49.762Z] Origin: none
[2025-07-13T14:36:49.762Z] Request Body: {"query":"mutation addItemToOrder($productVariantId: ID!, $quantity: Int!) {\n  addItemToOrder(productVariantId: $productVariantId, quantity: $quantity) {\n    ...CustomOrderDetail\n    ... on ErrorResult {\n      errorCode\n      message\n    }\n  }\n}\n\nfragment CustomOrderDetail on Order {\n  __typename\n  id\n  code\n  active\n  createdAt\n  state\n  currencyCode\n  couponCodes\n  discounts {\n    type\n    description\n    amountWithTax\n  }\n  totalQuantity\n  subTotal\n  subTotalWithTax\n  taxSummary {\n    description\n    taxRate\n    taxTotal\n  }\n  shippingWithTax\n  totalWithTax\n  customer {\n    id\n    firstName\n    lastName\n    emailAddress\n  }\n  shippingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  billingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  sh
[2025-07-13T14:36:49.762Z] Connection Remote Address: ::ffff:**********
[2025-07-13T14:36:49.762Z] Connection Remote Port: 55930
[2025-07-13T14:36:49.763Z] Process ID: 678758
[2025-07-13T14:36:49.763Z] Process Title: node /home/<USER>/damneddesigns/backend/dist/index.js
[2025-07-13T14:36:49.763Z] Node Version: v20.19.3
[2025-07-13T14:36:49.763Z] Server Hostname: damned
[2025-07-13T14:36:49.763Z] Stack Trace: Error
    at OrderCreationLoggerPlugin.getCleanStackTrace (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:65:15)
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:139:57)
[2025-07-13T14:36:49.763Z] Order contains NO line items (empty order)
[2025-07-13T14:36:49.764Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:156:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-13T14:36:49.764Z] ===== END ORDER DETAILS =====
[2025-07-13T14:39:15.699Z] ===== NEW ORDER CREATED =====
[2025-07-13T14:39:15.699Z] Order Code: DD29431
[2025-07-13T14:39:15.700Z] Order ID: 13368
[2025-07-13T14:39:15.700Z] Customer: guest
[2025-07-13T14:39:15.700Z] Total: 0
[2025-07-13T14:39:15.700Z] Request URL: /shop-api
[2025-07-13T14:39:15.700Z] Request Method: POST
[2025-07-13T14:39:15.700Z] User Agent: node
[2025-07-13T14:39:15.700Z] Referer: none
[2025-07-13T14:39:15.701Z] IP Address: ************
[2025-07-13T14:39:15.701Z] X-Forwarded-For: ************
[2025-07-13T14:39:15.701Z] Host: damneddesigns.com
[2025-07-13T14:39:15.701Z] Origin: none
[2025-07-13T14:39:15.701Z] Request Body: {"query":"mutation addItemToOrder($productVariantId: ID!, $quantity: Int!) {\n  addItemToOrder(productVariantId: $productVariantId, quantity: $quantity) {\n    ...CustomOrderDetail\n    ... on ErrorResult {\n      errorCode\n      message\n    }\n  }\n}\n\nfragment CustomOrderDetail on Order {\n  __typename\n  id\n  code\n  active\n  createdAt\n  state\n  currencyCode\n  couponCodes\n  discounts {\n    type\n    description\n    amountWithTax\n  }\n  totalQuantity\n  subTotal\n  subTotalWithTax\n  taxSummary {\n    description\n    taxRate\n    taxTotal\n  }\n  shippingWithTax\n  totalWithTax\n  customer {\n    id\n    firstName\n    lastName\n    emailAddress\n  }\n  shippingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  billingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  sh
[2025-07-13T14:39:15.701Z] Connection Remote Address: ::ffff:**********
[2025-07-13T14:39:15.701Z] Connection Remote Port: 37776
[2025-07-13T14:39:15.702Z] Process ID: 678801
[2025-07-13T14:39:15.702Z] Process Title: node /home/<USER>/damneddesigns/backend/dist/index.js
[2025-07-13T14:39:15.702Z] Server Hostname: damned
[2025-07-13T14:39:15.702Z] Node Version: v20.19.3
[2025-07-13T14:39:15.702Z] Stack Trace: Error
    at OrderCreationLoggerPlugin.getCleanStackTrace (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:65:15)
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:139:57)
[2025-07-13T14:39:15.702Z] Order contains NO line items (empty order)
[2025-07-13T14:39:15.703Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:156:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-13T14:39:15.703Z] ===== END ORDER DETAILS =====
[2025-07-13T14:42:24.322Z] ===== NEW ORDER CREATED =====
[2025-07-13T14:42:24.322Z] Order Code: DD29432
[2025-07-13T14:42:24.322Z] Order ID: 13369
[2025-07-13T14:42:24.323Z] Total: 0
[2025-07-13T14:42:24.323Z] Request URL: /shop-api
[2025-07-13T14:42:24.322Z] Customer: guest
[2025-07-13T14:42:24.323Z] Request Method: POST
[2025-07-13T14:42:24.323Z] User Agent: node
[2025-07-13T14:42:24.323Z] Referer: none
[2025-07-13T14:42:24.323Z] IP Address: ************
[2025-07-13T14:42:24.323Z] Host: damneddesigns.com
[2025-07-13T14:42:24.323Z] Origin: none
[2025-07-13T14:42:24.323Z] Request Body: {"query":"mutation addItemToOrder($productVariantId: ID!, $quantity: Int!) {\n  addItemToOrder(productVariantId: $productVariantId, quantity: $quantity) {\n    ...CustomOrderDetail\n    ... on ErrorResult {\n      errorCode\n      message\n    }\n  }\n}\n\nfragment CustomOrderDetail on Order {\n  __typename\n  id\n  code\n  active\n  createdAt\n  state\n  currencyCode\n  couponCodes\n  discounts {\n    type\n    description\n    amountWithTax\n  }\n  totalQuantity\n  subTotal\n  subTotalWithTax\n  taxSummary {\n    description\n    taxRate\n    taxTotal\n  }\n  shippingWithTax\n  totalWithTax\n  customer {\n    id\n    firstName\n    lastName\n    emailAddress\n  }\n  shippingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  billingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  sh
[2025-07-13T14:42:24.323Z] X-Forwarded-For: ************
[2025-07-13T14:42:24.323Z] Connection Remote Address: ::ffff:**********
[2025-07-13T14:42:24.323Z] Connection Remote Port: 40792
[2025-07-13T14:42:24.324Z] Process ID: 678801
[2025-07-13T14:42:24.324Z] Process Title: node /home/<USER>/damneddesigns/backend/dist/index.js
[2025-07-13T14:42:24.324Z] Node Version: v20.19.3
[2025-07-13T14:42:24.324Z] Server Hostname: damned
[2025-07-13T14:42:24.325Z] Stack Trace: Error
    at OrderCreationLoggerPlugin.getCleanStackTrace (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:65:15)
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:139:57)
[2025-07-13T14:42:24.325Z] Order contains NO line items (empty order)
[2025-07-13T14:42:24.326Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:156:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-13T14:42:24.326Z] ===== END ORDER DETAILS =====
[2025-07-13T14:52:00.486Z] ===== NEW ORDER CREATED =====
[2025-07-13T14:52:00.487Z] Order Code: DD29433
[2025-07-13T14:52:00.488Z] Order ID: 13370
[2025-07-13T14:52:00.488Z] Customer: guest
[2025-07-13T14:52:00.488Z] Total: 0
[2025-07-13T14:52:00.489Z] Request URL: /shop-api
[2025-07-13T14:52:00.489Z] Request Method: POST
[2025-07-13T14:52:00.489Z] User Agent: node
[2025-07-13T14:52:00.489Z] Referer: none
[2025-07-13T14:52:00.489Z] IP Address: ************
[2025-07-13T14:52:00.489Z] X-Forwarded-For: ************
[2025-07-13T14:52:00.489Z] Origin: none
[2025-07-13T14:52:00.489Z] Host: damneddesigns.com
[2025-07-13T14:52:00.489Z] Request Body: {"query":"mutation addItemToOrder($productVariantId: ID!, $quantity: Int!) {\n  addItemToOrder(productVariantId: $productVariantId, quantity: $quantity) {\n    ...CustomOrderDetail\n    ... on ErrorResult {\n      errorCode\n      message\n    }\n  }\n}\n\nfragment CustomOrderDetail on Order {\n  __typename\n  id\n  code\n  active\n  createdAt\n  state\n  currencyCode\n  couponCodes\n  discounts {\n    type\n    description\n    amountWithTax\n  }\n  totalQuantity\n  subTotal\n  subTotalWithTax\n  taxSummary {\n    description\n    taxRate\n    taxTotal\n  }\n  shippingWithTax\n  totalWithTax\n  customer {\n    id\n    firstName\n    lastName\n    emailAddress\n  }\n  shippingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  billingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  sh
[2025-07-13T14:52:00.489Z] Connection Remote Address: ::ffff:**********
[2025-07-13T14:52:00.493Z] Process Title: node /home/<USER>/damneddesigns/backend/dist/index.js
[2025-07-13T14:52:00.493Z] Process ID: 680485
[2025-07-13T14:52:00.490Z] Connection Remote Port: 51358
[2025-07-13T14:52:00.493Z] Node Version: v20.19.3
[2025-07-13T14:52:00.493Z] Server Hostname: damned
[2025-07-13T14:52:00.494Z] Stack Trace: Error
    at OrderCreationLoggerPlugin.getCleanStackTrace (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:65:15)
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:139:57)
[2025-07-13T14:52:00.494Z] Order contains NO line items (empty order)
[2025-07-13T14:52:00.494Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:156:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-13T14:52:00.494Z] ===== END ORDER DETAILS =====
[2025-07-13T14:55:26.525Z] ===== NEW ORDER CREATED =====
[2025-07-13T14:55:26.526Z] Order Code: DD29434
[2025-07-13T14:55:26.526Z] Order ID: 13371
[2025-07-13T14:55:26.526Z] Customer: guest
[2025-07-13T14:55:26.527Z] Total: 0
[2025-07-13T14:55:26.527Z] Request URL: /shop-api
[2025-07-13T14:55:26.527Z] Request Method: POST
[2025-07-13T14:55:26.527Z] User Agent: node
[2025-07-13T14:55:26.527Z] Referer: none
[2025-07-13T14:55:26.527Z] IP Address: ************
[2025-07-13T14:55:26.527Z] X-Forwarded-For: ************
[2025-07-13T14:55:26.527Z] Host: damneddesigns.com
[2025-07-13T14:55:26.527Z] Origin: none
[2025-07-13T14:55:26.528Z] Request Body: {"query":"mutation addItemToOrder($productVariantId: ID!, $quantity: Int!) {\n  addItemToOrder(productVariantId: $productVariantId, quantity: $quantity) {\n    ...CustomOrderDetail\n    ... on ErrorResult {\n      errorCode\n      message\n    }\n  }\n}\n\nfragment CustomOrderDetail on Order {\n  __typename\n  id\n  code\n  active\n  createdAt\n  state\n  currencyCode\n  couponCodes\n  discounts {\n    type\n    description\n    amountWithTax\n  }\n  totalQuantity\n  subTotal\n  subTotalWithTax\n  taxSummary {\n    description\n    taxRate\n    taxTotal\n  }\n  shippingWithTax\n  totalWithTax\n  customer {\n    id\n    firstName\n    lastName\n    emailAddress\n  }\n  shippingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  billingAddress {\n    fullName\n    streetLine1\n    streetLine2\n    company\n    city\n    province\n    postalCode\n    countryCode\n    phoneNumber\n  }\n  sh
[2025-07-13T14:55:26.528Z] Connection Remote Address: ::ffff:**********
[2025-07-13T14:55:26.528Z] Connection Remote Port: 47294
[2025-07-13T14:55:26.529Z] Process ID: 680909
[2025-07-13T14:55:26.529Z] Process Title: node /home/<USER>/damneddesigns/backend/dist/index.js
[2025-07-13T14:55:26.529Z] Node Version: v20.19.3
[2025-07-13T14:55:26.529Z] Stack Trace: Error
    at OrderCreationLoggerPlugin.getCleanStackTrace (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:65:15)
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:139:57)
[2025-07-13T14:55:26.529Z] Server Hostname: damned
[2025-07-13T14:55:26.530Z] ===== END ORDER DETAILS =====
[2025-07-13T14:55:26.529Z] Stack trace: Error
    at Object.next (/home/<USER>/damneddesigns/backend/dist/plugins/order-creation-logger.plugin.js:156:35)
    at ConsumerObserver.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:113:33)
    at Subscriber._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:80:26)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/filter.js:9:164
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
    at /home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/mergeInternals.js:28:28
    at OperatorSubscriber._this._next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/operators/OperatorSubscriber.js:33:21)
    at Subscriber.next (/home/<USER>/damneddesigns/backend/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/cjs/internal/Subscriber.js:51:18)
[2025-07-13T14:55:26.529Z] Order contains NO line items (empty order)
