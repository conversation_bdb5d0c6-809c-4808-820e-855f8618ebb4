# Bulk Order Fulfillment Guide

This document covers bulk fulfillment operations for Vendure orders, including tracking updates, fulfillment creation, and order state transitions.

## Overview

The bulk fulfillment system handles large-scale order processing operations that would be time-consuming to do manually through the Admin UI. It ensures proper fulfillment creation, tracking code assignment, and email notifications.

## Scripts Available

### 1. `bulk-tracking-cli.ts` - Main Bulk Tracking CLI

**Purpose**: Primary tool for bulk tracking number updates and order fulfillment.

**What it does**:
- Creates fulfillments for orders with tracking codes
- Sets tracking codes and shipping methods (FedEx, USPS, etc.)
- Creates fulfillment lines (links fulfillments to order lines)
- Transitions orders through proper states: `PaymentSettled` → `PartiallyShipped` → `Shipped`
- Triggers automatic email notifications to customers
- Skips orders that are already shipped

**Usage**:
```bash
# Dry run (safe testing - no changes made)
cd /home/<USER>/damneddesigns/database
pnpm exec ts-node bulk-tracking-cli.ts --dry-run

# Live run (makes actual changes)
cd /home/<USER>/damneddesigns/database
pnpm exec ts-node bulk-tracking-cli.ts

# With custom CSV file
pnpm exec ts-node bulk-tracking-cli.ts --file custom-tracking.csv
```

**CSV Format Required**:
```csv
order code,provider,tracking code
DD29217,fedex,390944013515
DD29218,USPS,9434650105501016682824
```

### 2. `bulk-fix-fulfillment-lines.ts` - Fulfillment Lines Repair Tool

**Purpose**: Fixes orders that have fulfillments but missing fulfillment lines (broken state).

**What it does**:
- Finds orders stuck in `PaymentSettled` state with `Shipped` fulfillments
- Creates missing fulfillment lines in `order_line_reference` table
- Transitions orders to proper `Shipped` state
- Triggers email notifications for fixed orders

**When to use**:
- When orders have fulfillments but won't transition to "Shipped" state
- After bulk operations that may have created incomplete fulfillments
- When customers aren't receiving shipping notifications despite having tracking codes

**Usage**:
```bash
cd /home/<USER>/damneddesigns/database
pnpm exec ts-node bulk-fix-fulfillment-lines.ts
```

**Note**: This script doesn't have a dry-run mode as it only fixes broken states.

### 3. `update-tracking.sh` - Shell Wrapper

**Purpose**: User-friendly shell wrapper for the bulk tracking CLI.

**Usage**:
```bash
cd /home/<USER>/damneddesigns/database
./update-tracking.sh --dry-run    # Safe testing
./update-tracking.sh              # Live run
```

## Database Structure

### Key Tables Involved

1. **`fulfillment`** - Stores fulfillment records
   - `id`, `trackingCode`, `method`, `state`, `handlerCode`

2. **`order_line_reference`** - Links fulfillments to order lines
   - `fulfillmentId`, `orderLineId`, `quantity`, `discriminator`
   - `discriminator = 'FulfillmentLine'` for fulfillment lines

3. **`order_fulfillments_fulfillment`** - Junction table
   - Links orders to their fulfillments

4. **`order`** - Order records
   - `state` field tracks order status

### Proper Fulfillment Structure

For an order to be properly fulfilled:
1. ✅ Fulfillment record exists with tracking code and method
2. ✅ Fulfillment lines exist in `order_line_reference` table
3. ✅ Order state is `Shipped`
4. ✅ Fulfillment state is `Shipped`

## Common Operations

### Bulk Tracking Update Process

1. **Prepare CSV file** with order codes, providers, and tracking codes
2. **Test with dry-run**: `pnpm exec ts-node bulk-tracking-cli.ts --dry-run`
3. **Review output** for any errors or issues
4. **Run live**: `pnpm exec ts-node bulk-tracking-cli.ts`
5. **Verify results** in Admin UI or database

### Troubleshooting Broken Fulfillments

**Symptoms**:
- Orders stuck in `PaymentSettled` state
- Fulfillments exist with tracking codes but orders won't ship
- Customers not receiving shipping notifications

**Solution**:
```bash
# Run the repair script
pnpm exec ts-node bulk-fix-fulfillment-lines.ts
```

**Verification Query**:
```sql
-- Check for broken fulfillments
SELECT 
    o.code as order_code,
    o.state as order_state,
    f.id as fulfillment_id,
    f.state as fulfillment_state,
    COUNT(olr.id) as fulfillment_line_count
FROM "order" o
JOIN order_fulfillments_fulfillment off ON o.id = off."orderId"
JOIN fulfillment f ON off."fulfillmentId" = f.id
LEFT JOIN order_line_reference olr ON f.id = olr."fulfillmentId" 
    AND olr.discriminator = 'FulfillmentLine'
WHERE o.state = 'PaymentSettled' 
    AND f.state = 'Shipped'
GROUP BY o.code, o.state, f.id, f.state
HAVING COUNT(olr.id) = 0;
```

## Historical Context

### Major Bulk Operations Completed

1. **Initial Bulk Tracking Update** (94 orders)
   - Successfully created fulfillments for 94 orders
   - Issue discovered: Missing fulfillment lines prevented order state transitions

2. **Fulfillment Lines Fix** (93 orders)
   - Used `bulk-fix-fulfillment-lines.ts` to repair broken fulfillments
   - Successfully transitioned all orders to `Shipped` state
   - Triggered email notifications for all customers

### Lessons Learned

1. **Fulfillment lines are critical** - Without them, orders cannot transition to shipped state
2. **Always test with dry-run** - Prevents accidental modifications to live orders
3. **Vendure's state machine is strict** - All order items must be properly linked to fulfillments
4. **Email notifications are automatic** - Proper state transitions trigger customer emails

## Safety Guidelines

### Before Running Bulk Operations

1. ✅ **Always use dry-run mode first**
2. ✅ **Backup the database** (if making major changes)
3. ✅ **Test with a small subset** of orders if possible
4. ✅ **Verify CSV format** matches expected structure
5. ✅ **Check for duplicate tracking codes** in the CSV

### During Operations

1. ✅ **Monitor the output logs** for errors
2. ✅ **Don't interrupt running processes** (can leave partial state)
3. ✅ **Keep track of processed orders** for verification

### After Operations

1. ✅ **Verify order states** in Admin UI
2. ✅ **Check customer email notifications** were sent
3. ✅ **Run verification queries** to confirm data integrity
4. ✅ **Document any issues** encountered

## Error Handling

### Common Errors and Solutions

**"Order not found"**
- Check order code format in CSV
- Verify orders exist in the system

**"null value in column 'method' violates not-null constraint"**
- Ensure provider field is not empty in CSV
- Check fulfillment handler configuration

**"Cannot transition Order to 'Shipped' state"**
- Run `bulk-fix-fulfillment-lines.ts` to create missing fulfillment lines
- Verify all order lines are properly linked

**"No fulfillment lines found"**
- Normal for new fulfillments - script will create them automatically
- If persistent, check `order_line_reference` table structure

## Monitoring and Verification

### Key Metrics to Track

- Number of orders processed successfully
- Number of orders skipped (already shipped)
- Number of errors encountered
- Email notifications sent count

### Verification Queries

```sql
-- Check order state distribution
SELECT state, COUNT(*) 
FROM "order" 
GROUP BY state;

-- Verify fulfillment completeness
SELECT 
    COUNT(DISTINCT f.id) as total_fulfillments,
    COUNT(DISTINCT olr."fulfillmentId") as fulfillments_with_lines
FROM fulfillment f
LEFT JOIN order_line_reference olr ON f.id = olr."fulfillmentId" 
    AND olr.discriminator = 'FulfillmentLine';
```

## Contact and Support

For issues with bulk fulfillment operations:
1. Check this documentation first
2. Review error logs in the script output
3. Run verification queries to understand the current state
4. Use dry-run mode to test fixes safely

---

*Last updated: July 2025*
*Scripts location: `/home/<USER>/damneddesigns/database/`*
