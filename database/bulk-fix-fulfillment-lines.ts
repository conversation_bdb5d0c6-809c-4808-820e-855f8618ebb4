#!/usr/bin/env ts-node

/**
 * Bulk fix missing fulfillment lines for all orders with fulfillments but no fulfillment lines
 * This will create the missing FulfillmentLine records and transition orders to Shipped state
 */

import { bootstrap, OrderService, RequestContext, Logger, ChannelService, TransactionalConnection } from '@vendure/core';
import { config } from '../backend/src/vendure-config';

interface BrokenFulfillment {
    orderId: number;
    orderCode: string;
    fulfillmentId: number;
    trackingCode: string;
    method: string;
    orderLines: Array<{
        id: number;
        quantity: number;
    }>;
}

async function bulkFixFulfillmentLines() {
    Logger.info('🔧 Starting bulk fix for missing fulfillment lines...', 'BulkFulfillmentFixer');
    
    // Create a modified config with a different port to avoid conflicts
    const cliConfig = {
        ...config,
        apiOptions: {
            ...config.apiOptions,
            port: 3006, // Use a different port
        }
    };
    
    // Bootstrap Vendure application
    const app = await bootstrap(cliConfig);
    
    try {
        // Get services
        const channelService = app.get(ChannelService);
        const orderService = app.get(OrderService);
        const connection = app.get(TransactionalConnection);
        const defaultChannel = await channelService.getDefaultChannel();
        const ctx = new RequestContext({
            apiType: 'admin',
            isAuthorized: true,
            authorizedAsOwnerOnly: false,
            channel: defaultChannel,
        });

        Logger.info('🔍 Finding all broken fulfillments...', 'BulkFulfillmentFixer');
        
        // Find all fulfillments that have no fulfillment lines
        const brokenFulfillments = await connection.rawConnection.query(`
            SELECT 
                o.id as "orderId",
                o.code as "orderCode",
                f.id as "fulfillmentId",
                f."trackingCode",
                f.method,
                f.state as "fulfillmentState"
            FROM "order" o
            JOIN order_fulfillments_fulfillment off ON o.id = off."orderId"
            JOIN fulfillment f ON off."fulfillmentId" = f.id
            LEFT JOIN order_line_reference olr ON f.id = olr."fulfillmentId" AND olr.discriminator = 'FulfillmentLine'
            WHERE o.state = 'PaymentSettled'
            AND f.state = 'Shipped'
            AND olr.id IS NULL
            ORDER BY o.code
        `);
        
        Logger.info(`Found ${brokenFulfillments.length} fulfillments missing fulfillment lines`, 'BulkFulfillmentFixer');
        
        if (brokenFulfillments.length === 0) {
            Logger.info('✅ No broken fulfillments found - all orders are properly configured', 'BulkFulfillmentFixer');
            return;
        }
        
        // Process each broken fulfillment
        let fixedCount = 0;
        let errorCount = 0;
        
        for (const fulfillment of brokenFulfillments) {
            try {
                const progress = `[${fixedCount + errorCount + 1}/${brokenFulfillments.length}]`;
                Logger.info(`${progress} Processing order ${fulfillment.orderCode} (fulfillment ${fulfillment.fulfillmentId})`, 'BulkFulfillmentFixer');
                
                // Get order lines for this order
                const orderLines = await connection.rawConnection.query(`
                    SELECT id, quantity
                    FROM order_line
                    WHERE "orderId" = $1
                `, [fulfillment.orderId]);
                
                Logger.info(`${progress} Found ${orderLines.length} order lines`, 'BulkFulfillmentFixer');
                
                // Create fulfillment line records for each order line
                for (const orderLine of orderLines) {
                    await connection.rawConnection.query(`
                        INSERT INTO order_line_reference (
                            "createdAt",
                            "updatedAt", 
                            quantity,
                            "fulfillmentId",
                            "orderLineId",
                            discriminator
                        ) VALUES (
                            NOW(),
                            NOW(),
                            $1,
                            $2,
                            $3,
                            'FulfillmentLine'
                        )
                    `, [orderLine.quantity, fulfillment.fulfillmentId, orderLine.id]);
                    
                    Logger.info(`${progress} Created fulfillment line for order line ${orderLine.id} (qty: ${orderLine.quantity})`, 'BulkFulfillmentFixer');
                }
                
                // Now transition the order to Shipped state
                Logger.info(`${progress} Transitioning order ${fulfillment.orderCode} to Shipped state...`, 'BulkFulfillmentFixer');
                
                // First transition: PaymentSettled → PartiallyShipped
                const partiallyShippedResult = await orderService.transitionToState(
                    ctx,
                    fulfillment.orderId,
                    'PartiallyShipped'
                );
                
                if (partiallyShippedResult instanceof Error) {
                    Logger.warn(`${progress} Could not transition to PartiallyShipped: ${partiallyShippedResult.message}`, 'BulkFulfillmentFixer');
                } else {
                    Logger.info(`${progress} ✅ Transitioned to PartiallyShipped`, 'BulkFulfillmentFixer');
                }
                
                // Final transition: PartiallyShipped → Shipped
                const shippedResult = await orderService.transitionToState(
                    ctx,
                    fulfillment.orderId,
                    'Shipped'
                );
                
                if (shippedResult instanceof Error) {
                    Logger.warn(`${progress} Could not transition to Shipped: ${shippedResult.message}`, 'BulkFulfillmentFixer');
                } else {
                    Logger.info(`${progress} ✅ Successfully transitioned to Shipped state`, 'BulkFulfillmentFixer');
                }
                
                fixedCount++;
                Logger.info(`${progress} ✅ Fixed order ${fulfillment.orderCode}`, 'BulkFulfillmentFixer');
                
            } catch (error) {
                errorCount++;
                Logger.error(`❌ Error fixing order ${fulfillment.orderCode}: ${error instanceof Error ? error.message : error}`, 'BulkFulfillmentFixer');
            }
        }
        
        Logger.info(`🎉 Bulk fix completed!`, 'BulkFulfillmentFixer');
        Logger.info(`✅ Successfully fixed: ${fixedCount} orders`, 'BulkFulfillmentFixer');
        Logger.info(`❌ Errors: ${errorCount} orders`, 'BulkFulfillmentFixer');
        
        if (fixedCount > 0) {
            Logger.info(`📧 Email notifications should now be triggered for ${fixedCount} customers`, 'BulkFulfillmentFixer');
        }
        
    } catch (error) {
        Logger.error(`❌ Error in bulk fix: ${error instanceof Error ? error.message : error}`, 'BulkFulfillmentFixer');
        throw error;
    } finally {
        await app.close();
    }
}

// Run the script
if (require.main === module) {
    bulkFixFulfillmentLines()
        .then(() => {
            Logger.info('✅ Bulk fulfillment lines fix completed successfully', 'BulkFulfillmentFixer');
            process.exit(0);
        })
        .catch((error) => {
            Logger.error(`❌ Bulk fulfillment lines fix failed: ${error instanceof Error ? error.message : error}`, 'BulkFulfillmentFixer');
            process.exit(1);
        });
}
