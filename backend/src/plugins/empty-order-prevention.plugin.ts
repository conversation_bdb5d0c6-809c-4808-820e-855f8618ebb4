import {
  VendurePlugin,
  PluginCommonModule,
  Logger,
  RequestContext
} from '@vendure/core';
import { Request, Response, NextFunction } from 'express';

/**
 * Plugin to prevent empty orders from being created by automated requests
 * Specifically targets the issue where "node" user agent creates empty orders
 */
@VendurePlugin({
  imports: [PluginCommonModule],
  configuration: config => {
    // Add middleware to intercept GraphQL requests
    config.apiOptions.middleware.push({
      route: '/shop-api',
      handler: (req: Request, res: Response, next: NextFunction) => {
        const userAgent = req.headers['user-agent'] || '';
        const referer = req.headers.referer || '';
        const body = req.body;

        // Check if this is a GraphQL request that might create an order
        if (req.method === 'POST' && body && body.query) {
          const query = body.query.toString();
          
          // Check for order-creating mutations
          const orderMutations = [
            'addItemToOrder',
            'adjustOrderLine',
            'removeOrderLine',
            'setOrderShippingAddress',
            'setOrderBillingAddress',
            'setCustomerForOrder'
          ];

          const isOrderMutation = orderMutations.some(mutation => 
            query.includes(mutation)
          );

          // Block suspicious requests that might create orders
          if (isOrderMutation && EmptyOrderPreventionPlugin.shouldBlockRequest(req)) {
            Logger.warn(
              `Blocked suspicious order mutation: ${query.substring(0, 100)}... from UA: ${userAgent}, IP: ${req.ip}`,
              'EmptyOrderPrevention'
            );
            
            return res.status(403).json({
              errors: [{
                message: 'Request blocked by security policy',
                extensions: {
                  code: 'SECURITY_BLOCK',
                  reason: 'Automated request detected'
                }
              }]
            });
          }

          // Also check for activeOrder queries from suspicious sources
          if (query.includes('activeOrder') && EmptyOrderPreventionPlugin.shouldBlockRequest(req)) {
            Logger.warn(
              `Blocked suspicious activeOrder query from UA: ${userAgent}, IP: ${req.ip}`,
              'EmptyOrderPrevention'
            );
            
            // Return empty result instead of blocking completely for activeOrder
            return res.json({
              data: {
                activeOrder: null
              }
            });
          }
        }

        next();
      }
    });

    return config;
  }
})
export class EmptyOrderPreventionPlugin {
  
  /**
   * Check if a request should be blocked based on suspicious patterns
   */
  static shouldBlockRequest(req: Request): boolean {
    const userAgent = req.headers['user-agent'] || '';
    const referer = req.headers.referer || '';
    const ip = req.ip || '';
    
    // Block requests with "node" user agent that have no referer (likely automated)
    if (userAgent === 'node' && !referer) {
      return true;
    }

    // Block requests from known monitoring/testing user agents
    const suspiciousUserAgents = [
      'SecurityTester',
      'HealthMonitor',
      'curl',
      'wget',
      'python-requests',
      'axios',
      'postman'
    ];

    if (suspiciousUserAgents.some(ua => userAgent.toLowerCase().includes(ua.toLowerCase()))) {
      return true;
    }

    // Block requests from internal Docker networks (common for health checks)
    if (ip.startsWith('172.') || ip.startsWith('10.') || ip === '127.0.0.1') {
      // Allow if it has a proper browser user agent
      const browserUserAgents = ['Mozilla', 'Chrome', 'Safari', 'Firefox', 'Edge'];
      const hasBrowserUA = browserUserAgents.some(ua => userAgent.includes(ua));
      
      if (!hasBrowserUA) {
        return true;
      }
    }

    return false;
  }

  static init(): typeof EmptyOrderPreventionPlugin {
    return EmptyOrderPreventionPlugin;
  }

  async onApplicationBootstrap() {
    Logger.info('EmptyOrderPreventionPlugin initialized - blocking suspicious order creation requests', 'EmptyOrderPrevention');
  }
}
