import {
    dummy<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    DefaultJobQueuePlugin,
    DefaultSearchPlugin,
    VendureConfig,
    AutoIncrementIdStrategy,
    LanguageCode,
    DefaultGuestCheckoutStrategy
} from '@vendure/core';
import { 
    defaultEmail<PERSON>and<PERSON>, 
    EmailPlugin, 
    FileBasedTemplateLoader,
    emailVerificationHandler,
    passwordResetHandler,
    emailAddressChangeHandler
} from '@vendure/email-plugin';
import { AssetServerPlugin, PresetOnlyStrategy } from '@vendure/asset-server-plugin';
import { AdminUiPlugin } from '@vendure/admin-ui-plugin';
import { GraphiqlPlugin } from '@vendure/graphiql-plugin';
import { RedisCachePlugin, DefaultSchedulerPlugin } from '@vendure/core';
import { HardenPlugin } from '@vendure/harden-plugin';
import { AuditPlugin } from './plugins/audit-plugin';
import { OrderCreationLoggerPlugin } from './plugins/order-creation-logger.plugin';
import { OrderDeduplicationPlugin } from './plugins/order-deduplication.plugin';
import { EmptyOrderPreventionPlugin } from './plugins/empty-order-prevention.plugin';
import { HighPerformanceQueuePlugin } from './plugins/high-performance-queue.plugin';
import { CustomShippingPlugin } from './plugins/custom-shipping';
import { SezzlePaymentPlugin } from './sezzle-payment';
import { orderFulfillmentHandler } from './email-handlers/order-fulfillment-handler';
import { orderConfirmationHandler } from './email-handlers/order-confirmation-handler';
import 'dotenv/config';
import path from 'path';
import { Request, Response, NextFunction } from 'express';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { NmiPaymentPlugin } from './plugins/nmi-payment';
import { SequentialOrderCodeStrategy } from './config/sequential-order-code-strategy';
import { NewsletterPlugin } from './plugins/newsletter';
import { createSecurityMiddleware } from './middleware/security-middleware';
import { SecurityPlugin } from './plugins/security-plugin';
import { StaleOrderCleanupPlugin } from './plugins/stale-order-cleanup.plugin';
import { CacheInvalidationPlugin } from './plugins/cache-invalidation.plugin';

function validateEnvironment() {
    const required: Record<string, 'string' | 'number' | 'boolean'> = {
        'DB_NAME': 'string',
        'DB_HOST': 'string',
        'DB_PORT': 'number',
        'DB_USERNAME': 'string',
        'DB_PASSWORD': 'string',
        'SUPERADMIN_USERNAME': 'string',
        'COOKIE_SECRET': 'string',
        'GMAIL_USER': 'string',
    };

    for (const [key, type] of Object.entries(required)) {
        const value = process.env[key];
        if (!value) {
            throw new Error(`Missing required environment variable: ${key}`);
        }
        if (type === 'number' && isNaN(Number(value))) {
            throw new Error(`Environment variable ${key} must be a number`);
        }
    }
}

validateEnvironment();

const IS_DEV = process.env.APP_ENV !== 'prod';
const serverPort = +process.env.PORT || 3000;

import { ExactStockDisplayStrategy } from './exact-stock-display-strategy';

export const config: VendureConfig = {
    entityOptions: {
        entityIdStrategy: new AutoIncrementIdStrategy(),
    },
    apiOptions: {
        port: serverPort,
        adminApiPath: 'admin-api',
        shopApiPath: 'shop-api',
        cors: {
            origin: IS_DEV
                ? ["http://localhost:3000", "http://localhost:4000", "http://localhost:8080"]
                : ['https://damneddesigns.com/admin', "https://damneddesigns.com"],
            credentials: true,
        },
        middleware: [
            {
                handler: (req: Request, res: Response, next: NextFunction) => {
                    req.app.set('trust proxy', ['127.0.0.1', '::1', '10.0.0.0/8', '**********/12', '***********/16']);
                    next();
                },
                route: '/',
                beforeListen: true,
            },
            // Security middleware for all routes
            {
                handler: createSecurityMiddleware(),
                route: '/',
            },
            ...(IS_DEV ? [] : [
                // Tiered rate limiting for different endpoints
                {
                    handler: rateLimit({
                        windowMs: 1 * 60 * 1000, // 1 minute window
                        max: 600, // Increased from 300 for high traffic
                        standardHeaders: true,
                        legacyHeaders: false,
                        message: 'Too many requests, please try again later.',
                        keyGenerator: (req) => {
                            // Different limits for different operations
                            const isOrderOperation = req.body?.query?.includes('addItemToOrder') ||
                                                   req.body?.query?.includes('adjustOrderLine');
                            return isOrderOperation ? `order:${req.ip}` : `general:${req.ip}`;
                        },
                        skip: (req) => {
                            // Skip rate limiting for health checks
                            return req.headers['user-agent']?.includes('HealthMonitor') ||
                                   req.url?.includes('health');
                        }
                    }),
                    route: '/shop-api',
                },
                // Separate rate limit for order operations (more restrictive)
                {
                    handler: rateLimit({
                        windowMs: 5 * 60 * 1000, // 5 minute window
                        max: 50, // Max 50 order operations per 5 minutes per IP
                        standardHeaders: true,
                        legacyHeaders: false,
                        message: 'Too many order operations, please slow down.',
                        keyGenerator: (req) => `order-ops:${req.ip}`,
                        skip: (req) => {
                            const isOrderOperation = req.body?.query?.includes('addItemToOrder') ||
                                                   req.body?.query?.includes('adjustOrderLine') ||
                                                   req.body?.query?.includes('setOrderShippingAddress');
                            return !isOrderOperation;
                        }
                    }),
                    route: '/shop-api',
                },
                {
                    handler: rateLimit({
                        windowMs: 15 * 60 * 1000,
                        max: 15000, // Increased from 10000 for high admin usage
                        standardHeaders: true,
                        legacyHeaders: false,
                        message: 'Too many admin requests, please try again later.',
                    }),
                    route: '/admin-api',
                },
            ]),
            {
                handler: helmet({
                    contentSecurityPolicy: {
                        directives: {
                            defaultSrc: ["'self'"],
                            styleSrc: ["'self'", "'unsafe-inline'"],
                            scriptSrc: [
                                "'self'",
                                "'unsafe-eval'",  // Allow in both dev and prod for translation functionality
                                "https://www.google.com/recaptcha/",
                                "https://www.gstatic.com/recaptcha/"
                            ],
                            frameSrc: [
                                "'self'",
                                "https://www.google.com/recaptcha/",
                                "https://recaptcha.google.com/recaptcha/"
                            ],
                            imgSrc: ["'self'", "data:", "https:"],
                            connectSrc: ["'self'", "https://www.google.com/recaptcha/"],
                            fontSrc: ["'self'"],
                            objectSrc: ["'none'"],
                            frameAncestors: ["'none'"],
                            ...(IS_DEV ? { 'worker-src': ["'self' blob:"] } : {})
                        },
                    },
                    hsts: {
                        maxAge: 31536000,
                        includeSubDomains: true,
                        preload: true
                    },
                    noSniff: true,
                    xssFilter: true,
                    referrerPolicy: { policy: "strict-origin-when-cross-origin" }
                }),
                route: '/',
            },
        ],
    },
    authOptions: {
        tokenMethod: ['bearer', 'cookie'],
        superadminCredentials: {
            identifier: process.env.SUPERADMIN_USERNAME,
            password: "",
        },
        cookieOptions: {
            name: '__vendure_session',
            secret: process.env.COOKIE_SECRET,
            httpOnly: true,
            secure: !IS_DEV,
            sameSite: 'strict',
            maxAge: 24 * 60 * 60 * 1000,
        },
    },
    dbConnectionOptions: {
        type: 'postgres',
        synchronize: false,
        migrations: [path.join(__dirname, './migrations/*.+(js|ts)')],
        logging: false,
        database: process.env.DB_NAME,
        schema: process.env.DB_SCHEMA,
        host: process.env.DB_HOST,
        port: +process.env.DB_PORT,
        username: process.env.DB_USERNAME,
        password: process.env.DB_PASSWORD,
        ssl: process.env.DB_SSL === 'true' ? {
            rejectUnauthorized: process.env.NODE_ENV === 'production',
            ca: process.env.DB_CA_CERT,
        } : false,
        extra: {
            // FIXED: Reduced connection pool to prevent "too many clients" error
            // With max instances + workers, total connections = instances × max_pool
            max: 8, // Reduced from 50 - safe for clustered setup
            min: 2, // Reduced minimum connections
            idleTimeoutMillis: 30000,
            connectionTimeoutMillis: 5000,
            statement_timeout: 60000,
            query_timeout: 60000,
            // Connection pool optimization
            acquireTimeoutMillis: 10000,
            createTimeoutMillis: 10000,
            destroyTimeoutMillis: 5000,
            reapIntervalMillis: 1000,
            createRetryIntervalMillis: 200,
            // Additional connection management
            evictionRunIntervalMillis: 10000, // Check for idle connections every 10s
            softIdleTimeoutMillis: 20000, // Soft timeout for idle connections
        },
    },
    paymentOptions: {
        paymentMethodHandlers: [
            dummyPaymentHandler,
        ],
    },
    orderOptions: {
        orderCodeStrategy: new SequentialOrderCodeStrategy(),
        guestCheckoutStrategy: new DefaultGuestCheckoutStrategy({
            allowGuestCheckoutForRegisteredCustomers: true,
        }),
    },
    customFields: {},
    catalogOptions: {
        stockDisplayStrategy: new ExactStockDisplayStrategy(),
    },
    plugins: [
        OrderCreationLoggerPlugin.init(),
        OrderDeduplicationPlugin.init(),
        EmptyOrderPreventionPlugin.init(),
        HighPerformanceQueuePlugin.init(),
        StaleOrderCleanupPlugin.init(),
        CacheInvalidationPlugin.init(),
        SecurityPlugin.init({
            enableLogging: true,
            enableGraphQLProtection: true,
            minRecaptchaScore: IS_DEV ? 0.3 : 0.5
        }),
        NewsletterPlugin,
        NmiPaymentPlugin,
        SezzlePaymentPlugin,
        CustomShippingPlugin,
        AuditPlugin,
        HardenPlugin.init({
            maxQueryComplexity: 10000,
            apiMode: process.env.APP_ENV !== 'prod' ? 'dev' : 'prod',
            logComplexityScore: process.env.APP_ENV !== 'prod',
        }),
        RedisCachePlugin.init({
            namespace: 'vendure-cache',
            maxItemSizeInBytes: 256_000, // Increased for larger cached objects
            redisOptions: {
                host: process.env.REDIS_HOST || '127.0.0.1',
                port: parseInt(process.env.REDIS_PORT || '6379', 10),
                password: process.env.REDIS_PASSWORD,
                // High-concurrency Redis optimization
                family: 4, // Force IPv4
                keepAlive: 30000, // Keep alive timeout in ms
                maxRetriesPerRequest: 3,
                connectTimeout: 5000,
                commandTimeout: 3000,
                lazyConnect: true, // Connect only when needed
                // Connection pool for high concurrency
                enableAutoPipelining: true, // Batch commands for better performance
                // Disable TLS for local Redis connections
                tls: process.env.REDIS_HOST !== '127.0.0.1' && process.env.NODE_ENV === 'production' ? {
                    rejectUnauthorized: true,
                } : undefined,
                retryStrategy: (times: number) => {
                    const delay = Math.min(times * 50, 2000); // Faster retry
                    return delay;
                },
                reconnectOnError: (err: Error) => {
                    console.error('Redis connection error:', err.message);
                    return err.message.includes('READONLY') || err.message.includes('ECONNRESET');
                }
            }
        }),
        AssetServerPlugin.init({
            route: 'assets',
            assetUploadDir: '/home/<USER>/damneddesigns/assets',
            assetUrlPrefix: process.env.ASSETS_URL || (IS_DEV ? 'http://localhost:3000/assets' : 'https://damneddesigns.com/assets'),
            presets: [
                // Default Vendure presets
                { name: 'tiny', width: 50, height: 50, mode: 'crop' },
                { name: 'thumb', width: 150, height: 150, mode: 'crop' },
                { name: 'small', width: 300, height: 300, mode: 'resize' },
                { name: 'medium', width: 500, height: 500, mode: 'resize' },
                { name: 'large', width: 800, height: 800, mode: 'resize' },
                // Custom high-resolution presets for product images
                { name: 'xl', width: 1200, height: 1200, mode: 'resize' },
                { name: 'xxl', width: 1600, height: 1600, mode: 'resize' },
                { name: 'modal', width: 1600, height: 2000, mode: 'resize' },
                // Ultra high-resolution presets for large monitors
                { name: 'ultra', width: 2048, height: 2048, mode: 'resize' },
                { name: '4k', width: 2560, height: 2560, mode: 'resize' },
            ],
            imageTransformStrategy: new PresetOnlyStrategy({
                defaultPreset: 'medium',
                permittedFormats: ['jpg', 'jpeg', 'png', 'webp', 'avif'], // Enable AVIF support
            }),
        }),
        DefaultJobQueuePlugin.init({ useDatabaseForBuffer: !IS_DEV }),
        DefaultSearchPlugin.init({ bufferUpdates: false, indexStockStatus: true }),
        IS_DEV
            ? EmailPlugin.init({
                devMode: true,
                outputPath: path.join(__dirname, '../static/email/test-emails'),
                route: 'mailbox',
                handlers: [
                    orderConfirmationHandler,  // Our custom order confirmation handler
                    emailVerificationHandler,  // Default handlers excluding order confirmation
                    passwordResetHandler,
                    emailAddressChangeHandler,
                    orderFulfillmentHandler    // Our custom fulfillment handler
                ],
                templateLoader: new FileBasedTemplateLoader(path.join(__dirname, '../static/email/templates')),
                globalTemplateVars: {
                    fromAddress: `\"${process.env.STORE_NAME || 'Damned Designs'}\" <${process.env.GMAIL_USER}>`,
                    verifyEmailAddressUrl: `${process.env.STOREFRONT_URL || 'https://damneddesigns.com'}/verify`,
                    passwordResetUrl: `${process.env.STOREFRONT_URL || 'https://damneddesigns.com'}/password-reset`,
                    changeEmailAddressUrl: `${process.env.STOREFRONT_URL || 'https://damneddesigns.com'}/verify-email-address-change`
                },
            })
            : EmailPlugin.init({
                outputPath: path.join(__dirname, '../static/email/test-emails'),
                route: 'mailbox',
                handlers: [
                    orderConfirmationHandler,  // Our custom order confirmation handler
                    emailVerificationHandler,  // Default handlers excluding order confirmation
                    passwordResetHandler,
                    emailAddressChangeHandler,
                    orderFulfillmentHandler    // Our custom fulfillment handler
                ],
                templateLoader: new FileBasedTemplateLoader(path.join(__dirname, '../static/email/templates')),
                        transport: {
                type: 'smtp',
                host: 'smtp.gmail.com',
                port: 587,
                secure: false, // true for 465, false for other ports
                auth: {
                    user: process.env.GMAIL_USER,
                    pass: process.env.EMAIL_PASS,
                },
            },
                globalTemplateVars: {
                    fromAddress: `\"${process.env.STORE_NAME || 'Damned Designs'}\" <${process.env.GMAIL_USER}>`,
                    verifyEmailAddressUrl: `${process.env.STOREFRONT_URL || 'https://damneddesigns.com'}/verify`,
                    passwordResetUrl: `${process.env.STOREFRONT_URL || 'https://damneddesigns.com'}/password-reset`,
                    changeEmailAddressUrl: `${process.env.STOREFRONT_URL || 'https://damneddesigns.com'}/verify-email-address-change`
                },
            }),
        AdminUiPlugin.init({
            route: 'admin',
            port: serverPort,
            adminUiConfig: {
                adminApiPath: 'admin-api',
                brand: process.env.STORE_NAME || 'Damned Designs',
                hideVendureBranding: true,
                defaultLanguage: LanguageCode.en,
                availableLanguages: [LanguageCode.en],
                loginImageUrl: 'https://damneddesigns.com/assets/preview/13/damned-mascot__preview.png',
            },
        }),
        ...(IS_DEV ? [GraphiqlPlugin.init()] : []),
        DefaultSchedulerPlugin.init(),
    ],
};